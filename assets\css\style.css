/* نمط مخصص لنظام إدارة الدورات التدريبية - الزام */

/* الخطوط والألوان الأساسية */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 15px;
    --box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    --transition: all 0.3s ease;
}

/* إعدادات عامة */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-color);
    line-height: 1.6;
}

/* تحسين الأزرار */
.btn-custom {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border: none;
    border-radius: 25px;
    padding: 12px 30px;
    color: white;
    font-weight: bold;
    transition: var(--transition);
    text-decoration: none;
    display: inline-block;
}

.btn-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    color: white;
}

/* تحسين البطاقات */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    padding: 20px;
}

/* الشريط الجانبي */
.sidebar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: 100vh;
    color: white;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    border-radius: 10px;
    margin: 5px 0;
    padding: 12px 15px;
    transition: var(--transition);
    font-weight: 500;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: rgba(255,255,255,0.2);
    color: white;
    transform: translateX(5px);
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

/* تحسين النماذج */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: var(--transition);
    font-size: 14px;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

.form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: var(--transition);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* شارات الحالة */
.status-badge {
    font-size: 0.8rem;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background-color: var(--warning-color);
    color: #000;
}

.status-approved {
    background-color: var(--success-color);
    color: #fff;
}

.status-rejected {
    background-color: var(--danger-color);
    color: #fff;
}

.status-available {
    background-color: var(--success-color);
    color: #fff;
}

.status-unavailable {
    background-color: var(--danger-color);
    color: #fff;
}

.status-completed {
    background-color: #6c757d;
    color: #fff;
}

/* بطاقات الإحصائيات */
.stat-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.stat-card.success {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
}

.stat-card.warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #fd7e14 100%);
}

.stat-card.info {
    background: linear-gradient(135deg, var(--info-color) 0%, #6f42c1 100%);
}

.stat-card:hover {
    transform: translateY(-5px) scale(1.02);
}

/* صور الدورات والطلاب */
.course-image,
.student-image {
    border-radius: 10px;
    transition: var(--transition);
    cursor: pointer;
}

.course-image:hover,
.student-image:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* تحسين الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background-color: #f8f9fa;
    border: none;
    font-weight: 600;
    color: var(--dark-color);
    padding: 15px;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

.table tbody td {
    padding: 15px;
    vertical-align: middle;
    border: none;
    border-bottom: 1px solid #e9ecef;
}

/* تحسين التبويبات */
.filter-tabs .nav-link {
    border-radius: 10px;
    margin: 0 5px;
    font-weight: 600;
    transition: var(--transition);
    border: 2px solid transparent;
}

.filter-tabs .nav-link.active {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    transform: translateY(-2px);
}

.filter-tabs .nav-link:hover {
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

/* تحسين التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 15px 20px;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.05));
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05));
    color: #856404;
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(23, 162, 184, 0.05));
    color: var(--info-color);
    border-left: 4px solid var(--info-color);
}

/* تحسين النوافذ المنبثقة */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-footer {
    border: none;
    padding: 20px;
}

/* تحسين أنشطة السجل */
.activity-item {
    border-left: 3px solid var(--primary-color);
    padding-left: 15px;
    margin-bottom: 15px;
    transition: var(--transition);
}

.activity-item:hover {
    background-color: rgba(102, 126, 234, 0.05);
    border-radius: 0 10px 10px 0;
    padding-right: 10px;
}

/* تحسين التنقل */
.breadcrumb {
    background: none;
    padding: 0;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb-item a:hover {
    color: var(--secondary-color);
    text-decoration: underline;
}

/* تحسين الأزرار الصغيرة */
.btn-group .btn {
    border-radius: 8px;
    margin: 0 2px;
    transition: var(--transition);
}

.btn-group .btn:hover {
    transform: translateY(-2px);
}

/* تحسين الصفحة الرئيسية */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 100px 0;
    text-align: center;
}

.feature-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
    transition: var(--transition);
}

.feature-icon:hover {
    transform: scale(1.1) rotate(5deg);
}

/* تحسين التصميم المتجاوب */
@media (max-width: 768px) {
    .sidebar {
        min-height: auto;
    }
    
    .card {
        margin-bottom: 20px;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
    
    .btn-custom {
        padding: 10px 20px;
        font-size: 14px;
    }
    
    .hero-section {
        padding: 50px 0;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
}

/* تحسين التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
}

/* تأثيرات الحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* تحسين النصوص */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
}

/* تحسين الروابط */
a {
    transition: var(--transition);
}

a:hover {
    text-decoration: none;
}

/* تحسين الأيقونات */
.fas, .fab {
    transition: var(--transition);
}

.card:hover .fas,
.card:hover .fab {
    transform: scale(1.1);
}

/* تحسين الحقول المطلوبة */
.form-label.required::after {
    content: " *";
    color: var(--danger-color);
    font-weight: bold;
}

/* تحسين حالة التحميل */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
