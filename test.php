<?php
// ملف اختبار بسيط
echo "<h1>اختبار النظام</h1>";
echo "<p>PHP يعمل بشكل صحيح!</p>";
echo "<p>إصدار PHP: " . PHP_VERSION . "</p>";
echo "<p>الوقت الحالي: " . date('Y-m-d H:i:s') . "</p>";

// اختبار الجلسات
session_start();
echo "<p>الجلسات تعمل: " . (session_status() === PHP_SESSION_ACTIVE ? 'نعم' : 'لا') . "</p>";

// اختبار قاعدة البيانات
echo "<h2>اختبار قاعدة البيانات</h2>";

try {
    // اختبار الاتصال المباشر
    $pdo = new PDO('mysql:host=localhost;charset=utf8mb4', 'root', '');
    echo "<p style='color: green;'>✅ الاتصال بـ MySQL ناجح</p>";
    
    // فحص وجود قاعدة البيانات
    $stmt = $pdo->query("SHOW DATABASES LIKE 'elzam_lms'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✅ قاعدة البيانات elzam_lms موجودة</p>";
        
        // الاتصال بقاعدة البيانات
        $pdo = new PDO('mysql:host=localhost;dbname=elzam_lms;charset=utf8mb4', 'root', '');
        echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات ناجح</p>";
        
        // فحص الجداول
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<p>الجداول الموجودة: " . implode(', ', $tables) . "</p>";
        
    } else {
        echo "<p style='color: red;'>❌ قاعدة البيانات elzam_lms غير موجودة</p>";
        echo "<p>يرجى إنشاء قاعدة البيانات أولاً:</p>";
        echo "<code>CREATE DATABASE elzam_lms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;</code>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>العودة للصفحة الرئيسية</a></p>";
?>
