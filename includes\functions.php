<?php
/**
 * وظائف النظام العامة
 */

require_once __DIR__ . '/../config/config.php';

/**
 * تسجيل نشاط في سجل النظام (محسن)
 */
function logActivity($student_id = null, $admin_id = null, $activity_type, $description = '') {
    global $pdo;

    // تجنب تسجيل الأنشطة غير المهمة في بيئة التطوير
    if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE === true) {
        return;
    }

    try {
        // استخدام prepared statement محسن
        static $stmt = null;
        if ($stmt === null) {
            $stmt = $pdo->prepare("
                INSERT INTO logs (student_id, admin_id, ip_address, activity_type, description)
                VALUES (?, ?, ?, ?, ?)
            ");
        }

        $stmt->execute([
            $student_id,
            $admin_id,
            getRealIpAddr(),
            $activity_type,
            $description
        ]);
    } catch (PDOException $e) {
        // تسجيل الخطأ بدون إيقاف التطبيق
        error_log("خطأ في تسجيل النشاط: " . $e->getMessage());
    }
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * التحقق من قوة كلمة المرور
 */
function validatePassword($password) {
    return strlen($password) >= PASSWORD_MIN_LENGTH;
}

/**
 * رفع ملف الصورة
 */
function uploadImage($file, $upload_path, $allowed_types = ALLOWED_IMAGE_TYPES) {
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'خطأ في رفع الملف'];
    }
    
    // التحقق من حجم الملف
    if ($file['size'] > MAX_FILE_SIZE) {
        return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
    }
    
    // التحقق من نوع الملف
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mime_type = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);
    
    if (!in_array($mime_type, $allowed_types)) {
        return ['success' => false, 'message' => 'نوع الملف غير مدعوم'];
    }
    
    // إنشاء اسم ملف فريد
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '_' . time() . '.' . $extension;
    $filepath = $upload_path . $filename;
    
    // رفع الملف
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return ['success' => true, 'filename' => $filename];
    } else {
        return ['success' => false, 'message' => 'فشل في رفع الملف'];
    }
}

/**
 * الحصول على معلومات الطالب
 */
function getStudentById($student_id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM students WHERE id = ?");
        $stmt->execute([$student_id]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        error_log("خطأ في جلب بيانات الطالب: " . $e->getMessage());
        return false;
    }
}

/**
 * الحصول على معلومات المشرف
 */
function getAdminById($admin_id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM admins WHERE id = ?");
        $stmt->execute([$admin_id]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        error_log("خطأ في جلب بيانات المشرف: " . $e->getMessage());
        return false;
    }
}

/**
 * الحصول على جميع الدورات المتاحة
 */
function getAvailableCourses() {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM courses WHERE status = 'متاح' ORDER BY created_at DESC");
        $stmt->execute();
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("خطأ في جلب الدورات: " . $e->getMessage());
        return [];
    }
}

/**
 * التحقق من تسجيل الطالب في دورة
 */
function isStudentEnrolled($student_id, $course_id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT id FROM enrollments WHERE student_id = ? AND course_id = ?");
        $stmt->execute([$student_id, $course_id]);
        return $stmt->fetch() !== false;
    } catch (PDOException $e) {
        error_log("خطأ في التحقق من التسجيل: " . $e->getMessage());
        return false;
    }
}

/**
 * تسجيل طالب في دورة
 */
function enrollStudent($student_id, $course_id) {
    global $pdo;
    
    try {
        // التحقق من عدم التسجيل المسبق
        if (isStudentEnrolled($student_id, $course_id)) {
            return ['success' => false, 'message' => 'أنت مسجل في هذه الدورة مسبقاً'];
        }
        
        $stmt = $pdo->prepare("INSERT INTO enrollments (student_id, course_id) VALUES (?, ?)");
        $stmt->execute([$student_id, $course_id]);
        
        // تسجيل النشاط
        logActivity($student_id, null, 'تسجيل في دورة', "تم التسجيل في الدورة رقم: $course_id");
        
        return ['success' => true, 'enrollment_id' => $pdo->lastInsertId()];
    } catch (PDOException $e) {
        error_log("خطأ في تسجيل الطالب: " . $e->getMessage());
        return ['success' => false, 'message' => 'خطأ في التسجيل'];
    }
}

/**
 * الحصول على دورات الطالب
 */
function getStudentCourses($student_id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT c.*, e.enrollment_date, e.payment_status, e.course_access 
            FROM courses c 
            JOIN enrollments e ON c.id = e.course_id 
            WHERE e.student_id = ? 
            ORDER BY e.enrollment_date DESC
        ");
        $stmt->execute([$student_id]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("خطأ في جلب دورات الطالب: " . $e->getMessage());
        return [];
    }
}

/**
 * تحديث حالة الدفع
 */
function updatePaymentStatus($enrollment_id, $status, $transaction_id = null) {
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        // تحديث حالة الدفع في جدول التسجيلات
        $stmt = $pdo->prepare("UPDATE enrollments SET payment_status = ? WHERE id = ?");
        $stmt->execute([$status, $enrollment_id]);
        
        // إضافة سجل دفع
        $stmt = $pdo->prepare("
            INSERT INTO payments (enrollment_id, amount, payment_method, transaction_id, payment_status)
            SELECT ?, c.price, 'تجريبي', ?, 'مكتمل'
            FROM enrollments e
            JOIN courses c ON e.course_id = c.id
            WHERE e.id = ?
        ");
        $stmt->execute([$enrollment_id, $transaction_id, $enrollment_id]);
        
        $pdo->commit();
        return true;
    } catch (PDOException $e) {
        $pdo->rollBack();
        error_log("خطأ في تحديث حالة الدفع: " . $e->getMessage());
        return false;
    }
}

/**
 * إرسال إشعار بريد إلكتروني (محسن ومحمي من الأخطاء)
 */
function sendNotification($to, $subject, $message) {
    // التحقق من تفعيل البريد الإلكتروني
    if (!defined('EMAIL_ENABLED') || !EMAIL_ENABLED) {
        // حفظ في السجل إذا كان مفعلاً
        if (defined('EMAIL_LOG_ENABLED') && EMAIL_LOG_ENABLED) {
            logEmailNotification($to, $subject, $message);
        }
        return true; // إرجاع نجاح وهمي
    }

    // محاولة الإرسال الفعلي مع حماية من الأخطاء
    try {
        // التحقق من صحة البريد الإلكتروني
        if (!filter_var($to, FILTER_VALIDATE_EMAIL)) {
            error_log("بريد إلكتروني غير صحيح: $to");
            return false;
        }

        $headers = "From: " . ADMIN_EMAIL . "\r\n";
        $headers .= "Reply-To: " . ADMIN_EMAIL . "\r\n";
        $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
        $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";

        // محاولة الإرسال مع قمع الأخطاء
        $result = @mail($to, $subject, $message, $headers);

        // تسجيل النتيجة
        if ($result) {
            error_log("تم إرسال إشعار بنجاح إلى: $to");
        } else {
            error_log("فشل في إرسال إشعار إلى: $to");
            // حفظ في السجل كبديل
            if (defined('EMAIL_LOG_ENABLED') && EMAIL_LOG_ENABLED) {
                logEmailNotification($to, $subject, $message, 'فشل الإرسال');
            }
        }

        return $result;

    } catch (Exception $e) {
        // تسجيل الخطأ بدون إيقاف التطبيق
        error_log("خطأ في إرسال البريد الإلكتروني: " . $e->getMessage());

        // حفظ في السجل كبديل
        if (defined('EMAIL_LOG_ENABLED') && EMAIL_LOG_ENABLED) {
            logEmailNotification($to, $subject, $message, 'خطأ: ' . $e->getMessage());
        }

        return false;
    }
}

/**
 * تسجيل الإشعار في ملف السجل
 */
function logEmailNotification($to, $subject, $message, $status = 'محفوظ في السجل') {
    $log_message = "=== إشعار بريد إلكتروني ===\n";
    $log_message .= "الحالة: $status\n";
    $log_message .= "إلى: $to\n";
    $log_message .= "الموضوع: $subject\n";
    $log_message .= "الرسالة: " . strip_tags($message) . "\n";
    $log_message .= "التاريخ: " . date('Y-m-d H:i:s') . "\n";
    $log_message .= "عنوان IP: " . getRealIpAddr() . "\n";
    $log_message .= "================================\n\n";

    // حفظ في ملف السجل
    $log_file = __DIR__ . '/../logs/email_notifications.log';

    // إنشاء مجلد السجلات إذا لم يكن موجوداً
    $log_dir = dirname($log_file);
    if (!file_exists($log_dir)) {
        @mkdir($log_dir, 0755, true);
    }

    // حفظ السجل مع حماية من الأخطاء
    @file_put_contents($log_file, $log_message, FILE_APPEND | LOCK_EX);
}

/**
 * تنسيق التاريخ للعرض
 */
function formatDate($date, $format = 'Y-m-d H:i:s') {
    return date($format, strtotime($date));
}

/**
 * تنسيق المبلغ للعرض
 */
function formatPrice($price) {
    return number_format($price, 2) . ' ريال';
}
?>
