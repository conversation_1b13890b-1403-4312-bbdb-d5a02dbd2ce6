<?php
/**
 * ملف إعداد النظام السريع
 */

echo "<h1>إعداد نظام الزام</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; direction: rtl; padding: 20px; }
    .success { color: green; background: #f0fff0; padding: 10px; border: 1px solid green; margin: 5px 0; }
    .error { color: red; background: #fff0f0; padding: 10px; border: 1px solid red; margin: 5px 0; }
    .warning { color: orange; background: #fff8f0; padding: 10px; border: 1px solid orange; margin: 5px 0; }
    .info { color: blue; background: #f0f8ff; padding: 10px; border: 1px solid blue; margin: 5px 0; }
    .btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
</style>";

$step = $_GET['step'] ?? 1;

if ($step == 1) {
    echo "<h2>الخطوة 1: فحص المتطلبات</h2>";
    
    // فحص PHP
    if (version_compare(PHP_VERSION, '7.4.0', '>=')) {
        echo "<div class='success'>✅ إصدار PHP مناسب: " . PHP_VERSION . "</div>";
    } else {
        echo "<div class='error'>❌ إصدار PHP قديم: " . PHP_VERSION . "</div>";
    }
    
    // فحص الامتدادات
    $extensions = ['pdo', 'pdo_mysql', 'mbstring'];
    foreach ($extensions as $ext) {
        if (extension_loaded($ext)) {
            echo "<div class='success'>✅ امتداد $ext متوفر</div>";
        } else {
            echo "<div class='error'>❌ امتداد $ext غير متوفر</div>";
        }
    }
    
    echo "<a href='?step=2' class='btn'>التالي: إنشاء قاعدة البيانات</a>";
    
} elseif ($step == 2) {
    echo "<h2>الخطوة 2: إنشاء قاعدة البيانات</h2>";
    
    try {
        // الاتصال بـ MySQL
        $pdo = new PDO('mysql:host=localhost;charset=utf8mb4', 'root', '');
        echo "<div class='success'>✅ الاتصال بـ MySQL ناجح</div>";
        
        // إنشاء قاعدة البيانات
        $pdo->exec("CREATE DATABASE IF NOT EXISTS elzam_lms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<div class='success'>✅ تم إنشاء قاعدة البيانات elzam_lms</div>";
        
        echo "<a href='?step=3' class='btn'>التالي: إنشاء الجداول</a>";
        
    } catch (PDOException $e) {
        echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
        echo "<div class='warning'>تأكد من تشغيل MySQL في XAMPP</div>";
    }
    
} elseif ($step == 3) {
    echo "<h2>الخطوة 3: إنشاء الجداول</h2>";
    
    try {
        $pdo = new PDO('mysql:host=localhost;dbname=elzam_lms;charset=utf8mb4', 'root', '');
        
        // قراءة ملف SQL
        $sql_file = 'sql/database.sql';
        if (file_exists($sql_file)) {
            $sql = file_get_contents($sql_file);
            
            // تقسيم الاستعلامات
            $queries = explode(';', $sql);
            
            foreach ($queries as $query) {
                $query = trim($query);
                if (!empty($query) && !preg_match('/^(CREATE DATABASE|USE)/i', $query)) {
                    try {
                        $pdo->exec($query);
                    } catch (PDOException $e) {
                        // تجاهل أخطاء الجداول الموجودة
                        if (strpos($e->getMessage(), 'already exists') === false) {
                            echo "<div class='warning'>تحذير: " . $e->getMessage() . "</div>";
                        }
                    }
                }
            }
            
            echo "<div class='success'>✅ تم إنشاء الجداول بنجاح</div>";
            
            // فحص الجداول
            $stmt = $pdo->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            echo "<div class='info'>الجداول المنشأة: " . implode(', ', $tables) . "</div>";
            
            echo "<a href='?step=4' class='btn'>التالي: اختبار النظام</a>";
            
        } else {
            echo "<div class='error'>❌ ملف sql/database.sql غير موجود</div>";
        }
        
    } catch (PDOException $e) {
        echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
    }
    
} elseif ($step == 4) {
    echo "<h2>الخطوة 4: اختبار النظام</h2>";
    
    try {
        require_once 'config/database.php';
        echo "<div class='success'>✅ ملف قاعدة البيانات يعمل</div>";
        
        // اختبار المشرف
        $stmt = $pdo->prepare("SELECT * FROM admins WHERE username = 'admin'");
        $stmt->execute();
        $admin = $stmt->fetch();
        
        if ($admin) {
            echo "<div class='success'>✅ المشرف الافتراضي موجود</div>";
            
            if (password_verify('admin123', $admin['password'])) {
                echo "<div class='success'>✅ كلمة مرور المشرف صحيحة</div>";
            } else {
                echo "<div class='error'>❌ كلمة مرور المشرف غير صحيحة</div>";
            }
        } else {
            echo "<div class='error'>❌ المشرف الافتراضي غير موجود</div>";
        }
        
        echo "<div class='success'>🎉 تم إعداد النظام بنجاح!</div>";
        echo "<div class='info'>
            <h3>بيانات تسجيل الدخول:</h3>
            <p><strong>اسم المستخدم:</strong> admin</p>
            <p><strong>كلمة المرور:</strong> admin123</p>
        </div>";
        
        echo "<a href='index.php' class='btn'>الصفحة الرئيسية</a>";
        echo "<a href='login.php?type=admin' class='btn'>تسجيل دخول المشرف</a>";
        echo "<a href='test.php' class='btn'>اختبار النظام</a>";
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
    }
}

echo "<hr>";
echo "<div class='info'>
    <h3>خطوات الإعداد:</h3>
    <ol>
        <li><a href='?step=1'>فحص المتطلبات</a></li>
        <li><a href='?step=2'>إنشاء قاعدة البيانات</a></li>
        <li><a href='?step=3'>إنشاء الجداول</a></li>
        <li><a href='?step=4'>اختبار النظام</a></li>
    </ol>
</div>";
?>
