-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS elzam_lms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE elzam_lms;

-- جدول المشرفين
CREATE TABLE admins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الطلاب
CREATE TABLE students (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id VARCHAR(20) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    university_stage ENUM('سنة أولى', 'سنة ثانية', 'سنة ثالثة', 'سنة رابعة') NOT NULL,
    major VARCHAR(100) NOT NULL,
    university_card VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    status ENUM('معلق', 'مقبول', 'مرفوض') DEFAULT 'معلق',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الدورات
CREATE TABLE courses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    course_name VARCHAR(200) NOT NULL,
    instructor VARCHAR(100) NOT NULL,
    duration VARCHAR(50) NOT NULL,
    expected_start_date DATE NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    cover_image VARCHAR(255),
    course_link VARCHAR(500),
    description TEXT,
    status ENUM('متاح', 'غير متاح', 'مكتمل') DEFAULT 'متاح',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول التسجيلات
CREATE TABLE enrollments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    course_id INT NOT NULL,
    enrollment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    payment_status ENUM('معلق', 'مدفوع', 'مرفوض') DEFAULT 'معلق',
    course_access ENUM('غير متاح', 'متاح') DEFAULT 'غير متاح',
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE KEY unique_enrollment (student_id, course_id)
);

-- جدول المدفوعات
CREATE TABLE payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    enrollment_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    transaction_id VARCHAR(100),
    payment_status ENUM('معلق', 'مكتمل', 'فاشل', 'مرتجع') DEFAULT 'معلق',
    payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (enrollment_id) REFERENCES enrollments(id) ON DELETE CASCADE
);

-- جدول سجلات النشاط
CREATE TABLE logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT,
    admin_id INT,
    ip_address VARCHAR(45) NOT NULL,
    activity_type VARCHAR(100) NOT NULL,
    description TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE SET NULL,
    FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE SET NULL
);

-- إدراج مشرف افتراضي (كلمة المرور: admin123)
INSERT INTO admins (username, email, password, full_name) VALUES
('admin', '<EMAIL>', '$2y$10$2lsIVg2st4c4nRGoo8kHMu7BB86fIp4RKx.RIyIkPRhmTfRpKD1Iu', 'مشرف النظام');

-- إدراج دورات تجريبية
INSERT INTO courses (course_name, instructor, duration, expected_start_date, price, description) VALUES 
('دورة البرمجة الأساسية', 'د. أحمد محمد', '4 أسابيع', '2024-02-01', 299.99, 'دورة شاملة في أساسيات البرمجة'),
('تطوير المواقع الإلكترونية', 'م. فاطمة علي', '6 أسابيع', '2024-02-15', 499.99, 'تعلم تطوير المواقع من الصفر'),
('قواعد البيانات المتقدمة', 'د. محمد حسن', '5 أسابيع', '2024-03-01', 399.99, 'إدارة وتصميم قواعد البيانات');
