# دليل إعداد البريد الإلكتروني - نظام الزام

## 📧 الوضع الحالي

النظام يعمل حالياً في **وضع التطوير** حيث:
- ✅ **لا يتم إرسال إيميلات حقيقية**
- ✅ **يتم حفظ الإشعارات في ملف السجل**
- ✅ **لا توجد أخطاء SMTP**
- ✅ **يمكن مراجعة الإشعارات من لوحة الإدارة**

## 🔧 إعداد البريد الإلكتروني للإنتاج

### الطريقة الأولى: Gmail SMTP (مجاني)

#### 1. إعد<PERSON> حساب Gmail:
```
1. فعل التحقق بخطوتين في حساب Gmail
2. اذهب إلى: إعدادات الحساب > الأمان > كلمات مرور التطبيقات
3. أنشئ كلمة مرور تطبيق جديدة
4. احفظ كلمة المرور (ستحتاجها لاحقاً)
```

#### 2. تثبيت PHPMailer:
```bash
composer require phpmailer/phpmailer
```

#### 3. تحديث إعدادات البريد:
```php
// في ملف config/email.php
define('SMTP_ENABLED', true);
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_ENCRYPTION', 'tls');
```

#### 4. تفعيل البريد الإلكتروني:
```php
// في ملف config/config.php
define('EMAIL_ENABLED', true);
```

### الطريقة الثانية: خدمات أخرى

#### SendGrid:
```php
define('SMTP_HOST', 'smtp.sendgrid.net');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', 'apikey');
define('SMTP_PASSWORD', 'your-sendgrid-api-key');
```

#### Mailgun:
```php
define('SMTP_HOST', 'smtp.mailgun.org');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', 'your-mailgun-username');
define('SMTP_PASSWORD', 'your-mailgun-password');
```

## 📊 مراقبة الإشعارات

### عرض سجل الإشعارات:
```
http://localhost/elzam-web-site/admin/email_logs.php
```

### ملف السجل المباشر:
```
logs/email_notifications.log
```

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة:

#### 1. خطأ SMTP Connection:
```
- تأكد من صحة بيانات SMTP
- تحقق من تفعيل "Less secure app access" (Gmail)
- استخدم كلمة مرور التطبيق بدلاً من كلمة المرور العادية
```

#### 2. خطأ Authentication:
```
- تأكد من صحة اسم المستخدم وكلمة المرور
- تحقق من تفعيل التحقق بخطوتين
- استخدم كلمة مرور تطبيق جديدة
```

#### 3. خطأ Port/Encryption:
```
- جرب Port 465 مع SSL
- جرب Port 587 مع TLS
- تأكد من إعدادات الجدار الناري
```

## 🔄 التبديل بين الأوضاع

### وضع التطوير (الحالي):
```php
define('EMAIL_ENABLED', false);
define('EMAIL_LOG_ENABLED', true);
```

### وضع الإنتاج:
```php
define('EMAIL_ENABLED', true);
define('EMAIL_LOG_ENABLED', true); // للاحتفاظ بالسجل
```

### إيقاف كامل:
```php
define('EMAIL_ENABLED', false);
define('EMAIL_LOG_ENABLED', false);
```

## 📝 قوالب الإشعارات

النظام يدعم القوالب التالية:
- ✅ **قبول الطالب** - `student_approved`
- ✅ **رفض الطالب** - `student_rejected`
- ✅ **تأكيد الدفع** - `payment_confirmation`

### إضافة قالب جديد:
```php
// في ملف config/email.php
$templates['new_template'] = [
    'subject' => 'عنوان الرسالة',
    'body' => 'محتوى الرسالة مع {متغيرات}'
];
```

## 🚀 الاستخدام

### إرسال إشعار بسيط:
```php
sendNotification($email, $subject, $message);
```

### إرسال إشعار بقالب:
```php
sendTemplatedNotification($email, 'student_approved', [
    'student_name' => 'أحمد محمد',
    'email' => '<EMAIL>',
    'login_url' => 'http://localhost/elzam-web-site/login.php'
]);
```

## 📈 الإحصائيات

يمكن مراقبة:
- ✅ **عدد الإشعارات المرسلة**
- ✅ **معدل النجاح/الفشل**
- ✅ **أوقات الإرسال**
- ✅ **أنواع الإشعارات**

## 🔒 الأمان

### نصائح مهمة:
- ✅ **لا تضع كلمات المرور في الكود**
- ✅ **استخدم متغيرات البيئة**
- ✅ **فعل SSL/TLS دائماً**
- ✅ **راقب سجلات الأخطاء**

### حماية ملف الإعدادات:
```apache
# في .htaccess
<Files "email.php">
    Order allow,deny
    Deny from all
</Files>
```

## 📞 الدعم

إذا واجهت مشاكل:
1. راجع ملف `logs/email_notifications.log`
2. تحقق من سجل أخطاء PHP
3. اختبر الإعدادات خطوة بخطوة
4. استخدم أدوات اختبار SMTP

---

**ملاحظة**: النظام يعمل حالياً بشكل مثالي بدون إرسال إيميلات حقيقية. تفعيل البريد الإلكتروني اختياري ومخصص للبيئة الإنتاجية.
