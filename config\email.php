<?php
/**
 * إعدادات البريد الإلكتروني
 */

// إعدادات SMTP (للاستخدام المستقبلي)
define('SMTP_ENABLED', false); // تغيير إلى true لتفعيل SMTP
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', ''); // بريدك الإلكتروني
define('SMTP_PASSWORD', ''); // كلمة مرور التطبيق
define('SMTP_ENCRYPTION', 'tls'); // tls أو ssl

/**
 * إرسال بريد إلكتروني باستخدام SMTP (PHPMailer)
 * يتطلب تثبيت PHPMailer: composer require phpmailer/phpmailer
 */
function sendEmailSMTP($to, $subject, $message, $from_name = 'نظام الزام') {
    if (!SMTP_ENABLED) {
        return false;
    }
    
    // التحقق من وجود PHPMailer
    if (!class_exists('PHPMailer\PHPMailer\PHPMailer')) {
        error_log("PHPMailer غير مثبت. استخدم: composer require phpmailer/phpmailer");
        return false;
    }
    
    try {
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        
        // إعدادات SMTP
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = SMTP_USERNAME;
        $mail->Password = SMTP_PASSWORD;
        $mail->SMTPSecure = SMTP_ENCRYPTION;
        $mail->Port = SMTP_PORT;
        $mail->CharSet = 'UTF-8';
        
        // إعدادات المرسل والمستقبل
        $mail->setFrom(SMTP_USERNAME, $from_name);
        $mail->addAddress($to);
        
        // محتوى الرسالة
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $message;
        
        $mail->send();
        return true;
        
    } catch (Exception $e) {
        error_log("خطأ في إرسال البريد الإلكتروني: " . $e->getMessage());
        return false;
    }
}

/**
 * قوالب البريد الإلكتروني
 */
function getEmailTemplate($type, $data = []) {
    $templates = [
        'student_approved' => [
            'subject' => 'تم قبول طلب التسجيل - منصة الزام',
            'body' => "
                <div style='font-family: Arial, sans-serif; direction: rtl; text-align: right;'>
                    <h2 style='color: #667eea;'>مرحباً {student_name}</h2>
                    <p>نحن سعداء لإبلاغك بأنه تم قبول طلب التسجيل الخاص بك في منصة الزام التعليمية.</p>
                    <p>يمكنك الآن تسجيل الدخول والتسجيل في الدورات المتاحة.</p>
                    <div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>
                        <h3>بيانات الدخول:</h3>
                        <p><strong>البريد الإلكتروني:</strong> {email}</p>
                        <p><strong>رابط تسجيل الدخول:</strong> <a href='{login_url}'>اضغط هنا</a></p>
                    </div>
                    <p>مع أطيب التحيات،<br>فريق منصة الزام</p>
                </div>
            "
        ],
        'student_rejected' => [
            'subject' => 'تحديث حالة طلب التسجيل - منصة الزام',
            'body' => "
                <div style='font-family: Arial, sans-serif; direction: rtl; text-align: right;'>
                    <h2 style='color: #dc3545;'>مرحباً {student_name}</h2>
                    <p>نأسف لإبلاغك بأنه لم يتم قبول طلب التسجيل الخاص بك في منصة الزام التعليمية.</p>
                    <p>يرجى التواصل معنا لمزيد من المعلومات على البريد الإلكتروني: " . ADMIN_EMAIL . "</p>
                    <p>مع أطيب التحيات،<br>فريق منصة الزام</p>
                </div>
            "
        ],
        'payment_confirmation' => [
            'subject' => 'تأكيد الدفع - منصة الزام',
            'body' => "
                <div style='font-family: Arial, sans-serif; direction: rtl; text-align: right;'>
                    <h2 style='color: #28a745;'>تم تأكيد الدفع</h2>
                    <p>مرحباً {student_name}</p>
                    <p>تم تأكيد دفعتك للدورة: <strong>{course_name}</strong></p>
                    <div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>
                        <h3>تفاصيل الدفع:</h3>
                        <p><strong>المبلغ:</strong> {amount}</p>
                        <p><strong>رقم المعاملة:</strong> {transaction_id}</p>
                        <p><strong>تاريخ الدفع:</strong> {payment_date}</p>
                    </div>
                    <p>يمكنك الآن الوصول إلى الدورة من لوحة التحكم الخاصة بك.</p>
                    <p>مع أطيب التحيات،<br>فريق منصة الزام</p>
                </div>
            "
        ]
    ];
    
    if (!isset($templates[$type])) {
        return null;
    }
    
    $template = $templates[$type];
    
    // استبدال المتغيرات في القالب
    foreach ($data as $key => $value) {
        $template['subject'] = str_replace('{' . $key . '}', $value, $template['subject']);
        $template['body'] = str_replace('{' . $key . '}', $value, $template['body']);
    }
    
    return $template;
}

/**
 * إرسال إشعار باستخدام القوالب
 */
function sendTemplatedNotification($to, $template_type, $data = []) {
    $template = getEmailTemplate($template_type, $data);
    
    if (!$template) {
        error_log("قالب البريد الإلكتروني غير موجود: $template_type");
        return false;
    }
    
    return sendNotification($to, $template['subject'], $template['body']);
}

/**
 * دليل إعداد البريد الإلكتروني
 */
function getEmailSetupGuide() {
    return "
    <div style='font-family: Arial, sans-serif; direction: rtl; padding: 20px;'>
        <h2>دليل إعداد البريد الإلكتروني</h2>
        
        <h3>1. إعداد Gmail SMTP:</h3>
        <ol>
            <li>فعل التحقق بخطوتين في حساب Gmail</li>
            <li>أنشئ كلمة مرور تطبيق من إعدادات الأمان</li>
            <li>استخدم البيانات التالية:
                <ul>
                    <li>SMTP Host: smtp.gmail.com</li>
                    <li>Port: 587</li>
                    <li>Encryption: TLS</li>
                </ul>
            </li>
        </ol>
        
        <h3>2. تثبيت PHPMailer:</h3>
        <code>composer require phpmailer/phpmailer</code>
        
        <h3>3. تحديث الإعدادات:</h3>
        <p>عدل ملف config/email.php وضع بياناتك</p>
        
        <h3>4. تفعيل SMTP:</h3>
        <p>غير SMTP_ENABLED إلى true</p>
    </div>
    ";
}
?>
