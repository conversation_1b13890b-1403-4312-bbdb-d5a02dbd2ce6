<?php
$error_code = $_GET['code'] ?? '404';

$errors = [
    '403' => [
        'title' => 'ممنوع الوصول',
        'message' => 'ليس لديك صلاحية للوصول إلى هذه الصفحة.',
        'icon' => 'fas fa-ban'
    ],
    '404' => [
        'title' => 'الصفحة غير موجودة',
        'message' => 'الصفحة التي تبحث عنها غير موجودة.',
        'icon' => 'fas fa-search'
    ],
    '500' => [
        'title' => 'خطأ في الخادم',
        'message' => 'حدث خطأ في الخادم. يرجى المحاولة لاحقاً.',
        'icon' => 'fas fa-exclamation-triangle'
    ]
];

$error = $errors[$error_code] ?? $errors['404'];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $error['title']; ?> - نظام الزام</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .error-container {
            text-align: center;
            color: white;
        }
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .error-icon {
            font-size: 4rem;
            margin-bottom: 2rem;
            opacity: 0.8;
        }
        .btn-home {
            background: rgba(255,255,255,0.2);
            border: 2px solid white;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .btn-home:hover {
            background: white;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <i class="<?php echo $error['icon']; ?>"></i>
        </div>
        <div class="error-code"><?php echo $error_code; ?></div>
        <h1 class="mb-3"><?php echo $error['title']; ?></h1>
        <p class="lead mb-4"><?php echo $error['message']; ?></p>
        <a href="index.php" class="btn-home">
            <i class="fas fa-home me-2"></i>
            العودة للصفحة الرئيسية
        </a>
    </div>
</body>
</html>
