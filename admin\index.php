<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول المشرف
requireAdminLogin();

// إحصائيات النظام
try {
    // عدد الطلاب
    $stmt = $pdo->query("SELECT COUNT(*) as total_students FROM students");
    $total_students = $stmt->fetch()['total_students'];
    
    // عدد الطلاب المعلقين
    $stmt = $pdo->query("SELECT COUNT(*) as pending_students FROM students WHERE status = 'معلق'");
    $pending_students = $stmt->fetch()['pending_students'];
    
    // عدد الدورات
    $stmt = $pdo->query("SELECT COUNT(*) as total_courses FROM courses");
    $total_courses = $stmt->fetch()['total_courses'];
    
    // عدد التسجيلات
    $stmt = $pdo->query("SELECT COUNT(*) as total_enrollments FROM enrollments");
    $total_enrollments = $stmt->fetch()['total_enrollments'];
    
    // إجمالي الإيرادات
    $stmt = $pdo->query("SELECT SUM(amount) as total_revenue FROM payments WHERE payment_status = 'مكتمل'");
    $total_revenue = $stmt->fetch()['total_revenue'] ?? 0;
    
    // آخر الطلاب المسجلين
    $stmt = $pdo->query("SELECT * FROM students ORDER BY created_at DESC LIMIT 5");
    $recent_students = $stmt->fetchAll();
    
    // آخر الأنشطة
    $stmt = $pdo->query("
        SELECT l.*, s.full_name as student_name, a.full_name as admin_name 
        FROM logs l 
        LEFT JOIN students s ON l.student_id = s.id 
        LEFT JOIN admins a ON l.admin_id = a.id 
        ORDER BY l.timestamp DESC 
        LIMIT 10
    ");
    $recent_activities = $stmt->fetchAll();
    
} catch (PDOException $e) {
    error_log("خطأ في جلب الإحصائيات: " . $e->getMessage());
    $total_students = $pending_students = $total_courses = $total_enrollments = $total_revenue = 0;
    $recent_students = $recent_activities = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المشرف - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 10px;
            margin: 5px 0;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.2);
            color: white;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .stat-card {
            background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark) 100%);
            color: white;
        }
        .stat-card.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .stat-card.warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }
        .stat-card.info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }
        .activity-item {
            border-left: 3px solid #667eea;
            padding-left: 15px;
            margin-bottom: 15px;
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .status-pending { background-color: #ffc107; color: #000; }
        .status-approved { background-color: #28a745; color: #fff; }
        .status-rejected { background-color: #dc3545; color: #fff; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <i class="fas fa-shield-alt fa-3x mb-2"></i>
                    <h5 class="fw-bold">لوحة الإدارة</h5>
                </div>
                
                <div class="mb-4">
                    <div class="text-center">
                        <i class="fas fa-user-shield fa-2x mb-2"></i>
                        <h6 class="fw-bold"><?php echo htmlspecialchars($_SESSION['admin_name']); ?></h6>
                        <small>مشرف النظام</small>
                    </div>
                </div>
                
                <nav class="nav flex-column">
                    <a class="nav-link active" href="index.php">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة التحكم
                    </a>
                    <a class="nav-link" href="students.php">
                        <i class="fas fa-users me-2"></i>
                        إدارة الطلاب
                        <?php if ($pending_students > 0): ?>
                            <span class="badge bg-warning ms-2"><?php echo $pending_students; ?></span>
                        <?php endif; ?>
                    </a>
                    <a class="nav-link" href="courses.php">
                        <i class="fas fa-book me-2"></i>
                        إدارة الدورات
                    </a>
                    <a class="nav-link" href="enrollments.php">
                        <i class="fas fa-clipboard-list me-2"></i>
                        التسجيلات
                    </a>
                    <a class="nav-link" href="payments.php">
                        <i class="fas fa-credit-card me-2"></i>
                        المدفوعات
                    </a>
                    <a class="nav-link" href="logs.php">
                        <i class="fas fa-history me-2"></i>
                        سجل الأنشطة
                    </a>
                    <hr class="my-3">
                    <a class="nav-link text-warning" href="../logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>
                        تسجيل الخروج
                    </a>
                </nav>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="fw-bold">لوحة التحكم الرئيسية</h2>
                    <div class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        <?php echo date('Y-m-d H:i'); ?>
                    </div>
                </div>
                
                <!-- Statistics Cards -->
                <div class="row g-4 mb-4">
                    <div class="col-md-3">
                        <div class="card stat-card">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h4 class="fw-bold"><?php echo $total_students; ?></h4>
                                <p class="mb-0">إجمالي الطلاب</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <h4 class="fw-bold"><?php echo $pending_students; ?></h4>
                                <p class="mb-0">طلاب معلقين</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-book fa-2x mb-2"></i>
                                <h4 class="fw-bold"><?php echo $total_courses; ?></h4>
                                <p class="mb-0">الدورات المتاحة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-line fa-2x mb-2"></i>
                                <h4 class="fw-bold"><?php echo formatPrice($total_revenue); ?></h4>
                                <p class="mb-0">إجمالي الإيرادات</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row g-4">
                    <!-- Recent Students -->
                    <div class="col-lg-6">
                        <div class="card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="fw-bold mb-0">
                                    <i class="fas fa-user-plus me-2"></i>
                                    آخر الطلاب المسجلين
                                </h5>
                                <a href="students.php" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_students)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا يوجد طلاب مسجلين</p>
                                    </div>
                                <?php else: ?>
                                    <div class="list-group list-group-flush">
                                        <?php foreach ($recent_students as $student): ?>
                                            <div class="list-group-item border-0 px-0">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <h6 class="fw-bold mb-1"><?php echo htmlspecialchars($student['full_name']); ?></h6>
                                                        <small class="text-muted">
                                                            <?php echo htmlspecialchars($student['email']); ?>
                                                        </small>
                                                    </div>
                                                    <div class="text-end">
                                                        <span class="status-badge status-<?php echo $student['status'] === 'مقبول' ? 'approved' : ($student['status'] === 'مرفوض' ? 'rejected' : 'pending'); ?>">
                                                            <?php echo $student['status']; ?>
                                                        </span>
                                                        <br>
                                                        <small class="text-muted">
                                                            <?php echo formatDate($student['created_at'], 'Y-m-d'); ?>
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Activities -->
                    <div class="col-lg-6">
                        <div class="card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="fw-bold mb-0">
                                    <i class="fas fa-history me-2"></i>
                                    آخر الأنشطة
                                </h5>
                                <a href="logs.php" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_activities)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد أنشطة</p>
                                    </div>
                                <?php else: ?>
                                    <div style="max-height: 400px; overflow-y: auto;">
                                        <?php foreach ($recent_activities as $activity): ?>
                                            <div class="activity-item">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <h6 class="fw-bold mb-1"><?php echo htmlspecialchars($activity['activity_type']); ?></h6>
                                                        <p class="mb-1 small">
                                                            <?php if ($activity['student_name']): ?>
                                                                الطالب: <?php echo htmlspecialchars($activity['student_name']); ?>
                                                            <?php elseif ($activity['admin_name']): ?>
                                                                المشرف: <?php echo htmlspecialchars($activity['admin_name']); ?>
                                                            <?php else: ?>
                                                                مستخدم غير معروف
                                                            <?php endif; ?>
                                                        </p>
                                                        <?php if ($activity['description']): ?>
                                                            <p class="mb-1 small text-muted">
                                                                <?php echo htmlspecialchars($activity['description']); ?>
                                                            </p>
                                                        <?php endif; ?>
                                                    </div>
                                                    <small class="text-muted">
                                                        <?php echo formatDate($activity['timestamp'], 'H:i'); ?>
                                                    </small>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="row g-4 mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="fw-bold mb-0">
                                    <i class="fas fa-bolt me-2"></i>
                                    إجراءات سريعة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <a href="students.php?filter=pending" class="btn btn-warning w-100">
                                            <i class="fas fa-user-clock me-2"></i>
                                            مراجعة الطلاب المعلقين
                                            <?php if ($pending_students > 0): ?>
                                                <span class="badge bg-light text-dark ms-2"><?php echo $pending_students; ?></span>
                                            <?php endif; ?>
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="courses.php?action=add" class="btn btn-success w-100">
                                            <i class="fas fa-plus me-2"></i>
                                            إضافة دورة جديدة
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="enrollments.php" class="btn btn-info w-100">
                                            <i class="fas fa-list me-2"></i>
                                            عرض التسجيلات
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="logs.php" class="btn btn-secondary w-100">
                                            <i class="fas fa-chart-bar me-2"></i>
                                            تقارير النظام
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث الوقت كل دقيقة
        setInterval(function() {
            const now = new Date();
            const timeString = now.getFullYear() + '-' + 
                              String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                              String(now.getDate()).padStart(2, '0') + ' ' +
                              String(now.getHours()).padStart(2, '0') + ':' + 
                              String(now.getMinutes()).padStart(2, '0');
            
            const timeElements = document.querySelectorAll('.text-muted i.fa-calendar');
            timeElements.forEach(el => {
                if (el.parentNode.textContent.includes('<?php echo date("Y-m-d"); ?>')) {
                    el.parentNode.innerHTML = '<i class="fas fa-calendar me-1"></i>' + timeString;
                }
            });
        }, 60000);
    </script>
</body>
</html>
