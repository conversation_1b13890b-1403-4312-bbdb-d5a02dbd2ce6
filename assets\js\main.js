/**
 * ملف JavaScript الرئيسي لنظام إدارة الدورات التدريبية - الزام
 */

// إعدادات عامة
const CONFIG = {
    MAX_FILE_SIZE: 2 * 1024 * 1024, // 2MB
    ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/jpg'],
    SESSION_TIMEOUT: 3600000, // ساعة واحدة بالميلي ثانية
    AUTO_SAVE_INTERVAL: 30000 // 30 ثانية
};

// دالة تهيئة النظام
document.addEventListener('DOMContentLoaded', function() {
    initializeSystem();
});

/**
 * تهيئة النظام
 */
function initializeSystem() {
    // تهيئة التنبيهات
    initializeAlerts();
    
    // تهيئة النماذج
    initializeForms();
    
    // تهيئة رفع الملفات
    initializeFileUploads();
    
    // تهيئة الجداول
    initializeTables();
    
    // تهيئة النوافذ المنبثقة
    initializeModals();
    
    // تهيئة مراقبة الجلسة
    initializeSessionMonitoring();
    
    // تهيئة التأثيرات البصرية
    initializeAnimations();
    
    console.log('تم تهيئة النظام بنجاح');
}

/**
 * تهيئة التنبيهات
 */
function initializeAlerts() {
    // إخفاء التنبيهات تلقائياً بعد 5 ثوان
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert && alert.parentNode) {
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.remove();
                }, 300);
            }
        }, 5000);
    });
}

/**
 * عرض تنبيه مخصص
 */
function showAlert(message, type = 'info', duration = 5000) {
    const alertContainer = document.getElementById('alert-container') || createAlertContainer();
    
    const alertElement = document.createElement('div');
    alertElement.className = `alert alert-${type} alert-dismissible fade show`;
    alertElement.innerHTML = `
        <i class="fas fa-${getAlertIcon(type)} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.appendChild(alertElement);
    
    // إخفاء التنبيه تلقائياً
    if (duration > 0) {
        setTimeout(() => {
            if (alertElement && alertElement.parentNode) {
                alertElement.style.opacity = '0';
                setTimeout(() => {
                    alertElement.remove();
                }, 300);
            }
        }, duration);
    }
}

/**
 * إنشاء حاوية التنبيهات
 */
function createAlertContainer() {
    const container = document.createElement('div');
    container.id = 'alert-container';
    container.style.position = 'fixed';
    container.style.top = '20px';
    container.style.right = '20px';
    container.style.zIndex = '9999';
    container.style.maxWidth = '400px';
    document.body.appendChild(container);
    return container;
}

/**
 * الحصول على أيقونة التنبيه
 */
function getAlertIcon(type) {
    const icons = {
        'success': 'check-circle',
        'danger': 'exclamation-triangle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * تهيئة النماذج
 */
function initializeForms() {
    // التحقق من صحة النماذج
    const forms = document.querySelectorAll('form[data-validate="true"]');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
                showAlert('يرجى تصحيح الأخطاء في النموذج', 'danger');
            }
        });
    });
    
    // التحقق من تطابق كلمات المرور
    const passwordFields = document.querySelectorAll('input[type="password"][data-confirm]');
    passwordFields.forEach(field => {
        const confirmField = document.getElementById(field.dataset.confirm);
        if (confirmField) {
            [field, confirmField].forEach(input => {
                input.addEventListener('input', () => {
                    validatePasswordMatch(field, confirmField);
                });
            });
        }
    });
    
    // حفظ تلقائي للنماذج الطويلة
    const autoSaveForms = document.querySelectorAll('form[data-autosave="true"]');
    autoSaveForms.forEach(form => {
        setInterval(() => {
            autoSaveForm(form);
        }, CONFIG.AUTO_SAVE_INTERVAL);
    });
}

/**
 * التحقق من صحة النموذج
 */
function validateForm(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            markFieldAsInvalid(field, 'هذا الحقل مطلوب');
            isValid = false;
        } else {
            markFieldAsValid(field);
        }
    });
    
    // التحقق من البريد الإلكتروني
    const emailFields = form.querySelectorAll('input[type="email"]');
    emailFields.forEach(field => {
        if (field.value && !isValidEmail(field.value)) {
            markFieldAsInvalid(field, 'البريد الإلكتروني غير صحيح');
            isValid = false;
        }
    });
    
    return isValid;
}

/**
 * وضع علامة على الحقل كغير صحيح
 */
function markFieldAsInvalid(field, message) {
    field.classList.add('is-invalid');
    field.classList.remove('is-valid');
    
    let feedback = field.parentNode.querySelector('.invalid-feedback');
    if (!feedback) {
        feedback = document.createElement('div');
        feedback.className = 'invalid-feedback';
        field.parentNode.appendChild(feedback);
    }
    feedback.textContent = message;
}

/**
 * وضع علامة على الحقل كصحيح
 */
function markFieldAsValid(field) {
    field.classList.add('is-valid');
    field.classList.remove('is-invalid');
    
    const feedback = field.parentNode.querySelector('.invalid-feedback');
    if (feedback) {
        feedback.remove();
    }
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * التحقق من تطابق كلمات المرور
 */
function validatePasswordMatch(passwordField, confirmField) {
    if (passwordField.value !== confirmField.value) {
        markFieldAsInvalid(confirmField, 'كلمات المرور غير متطابقة');
    } else if (passwordField.value.length >= 8) {
        markFieldAsValid(confirmField);
    }
}

/**
 * تهيئة رفع الملفات
 */
function initializeFileUploads() {
    const fileInputs = document.querySelectorAll('input[type="file"]');
    
    fileInputs.forEach(input => {
        input.addEventListener('change', function(e) {
            validateFileUpload(this);
        });
        
        // إضافة منطقة السحب والإفلات
        addDragAndDrop(input);
    });
}

/**
 * التحقق من صحة رفع الملف
 */
function validateFileUpload(input) {
    const file = input.files[0];
    if (!file) return;
    
    // التحقق من حجم الملف
    if (file.size > CONFIG.MAX_FILE_SIZE) {
        showAlert('حجم الملف كبير جداً! الحد الأقصى 2 ميجابايت.', 'danger');
        input.value = '';
        return false;
    }
    
    // التحقق من نوع الملف
    if (input.accept && !isValidFileType(file, input.accept)) {
        showAlert('نوع الملف غير مدعوم!', 'danger');
        input.value = '';
        return false;
    }
    
    // عرض معاينة للصور
    if (file.type.startsWith('image/')) {
        showImagePreview(file, input);
    }
    
    return true;
}

/**
 * التحقق من نوع الملف
 */
function isValidFileType(file, acceptedTypes) {
    const types = acceptedTypes.split(',').map(type => type.trim());
    return types.some(type => {
        if (type.startsWith('.')) {
            return file.name.toLowerCase().endsWith(type.toLowerCase());
        } else {
            return file.type === type;
        }
    });
}

/**
 * عرض معاينة الصورة
 */
function showImagePreview(file, input) {
    const reader = new FileReader();
    reader.onload = function(e) {
        let preview = input.parentNode.querySelector('.image-preview');
        if (!preview) {
            preview = document.createElement('div');
            preview.className = 'image-preview mt-2';
            input.parentNode.appendChild(preview);
        }
        
        preview.innerHTML = `
            <img src="${e.target.result}" class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
            <button type="button" class="btn btn-sm btn-danger ms-2" onclick="removeImagePreview(this)">
                <i class="fas fa-times"></i>
            </button>
        `;
    };
    reader.readAsDataURL(file);
}

/**
 * إزالة معاينة الصورة
 */
function removeImagePreview(button) {
    const preview = button.parentNode;
    const input = preview.parentNode.querySelector('input[type="file"]');
    input.value = '';
    preview.remove();
}

/**
 * إضافة وظيفة السحب والإفلات
 */
function addDragAndDrop(input) {
    const dropZone = input.parentNode;
    
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, preventDefaults, false);
    });
    
    ['dragenter', 'dragover'].forEach(eventName => {
        dropZone.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, unhighlight, false);
    });
    
    dropZone.addEventListener('drop', handleDrop, false);
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    function highlight(e) {
        dropZone.classList.add('drag-over');
    }
    
    function unhighlight(e) {
        dropZone.classList.remove('drag-over');
    }
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length > 0) {
            input.files = files;
            validateFileUpload(input);
        }
    }
}

/**
 * تهيئة الجداول
 */
function initializeTables() {
    // إضافة وظيفة البحث للجداول
    const searchInputs = document.querySelectorAll('input[data-table-search]');
    searchInputs.forEach(input => {
        const tableId = input.dataset.tableSearch;
        const table = document.getElementById(tableId);
        if (table) {
            input.addEventListener('input', () => {
                searchTable(table, input.value);
            });
        }
    });
    
    // إضافة وظيفة الترتيب للجداول
    const sortableHeaders = document.querySelectorAll('th[data-sortable]');
    sortableHeaders.forEach(header => {
        header.style.cursor = 'pointer';
        header.addEventListener('click', () => {
            sortTable(header);
        });
    });
}

/**
 * البحث في الجدول
 */
function searchTable(table, searchTerm) {
    const rows = table.querySelectorAll('tbody tr');
    const term = searchTerm.toLowerCase();
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(term)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

/**
 * ترتيب الجدول
 */
function sortTable(header) {
    const table = header.closest('table');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(header.parentNode.children).indexOf(header);
    const isAscending = header.classList.contains('sort-asc');
    
    // إزالة علامات الترتيب من جميع الأعمدة
    header.parentNode.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
    });
    
    // ترتيب الصفوف
    rows.sort((a, b) => {
        const aText = a.children[columnIndex].textContent.trim();
        const bText = b.children[columnIndex].textContent.trim();
        
        if (isAscending) {
            return bText.localeCompare(aText, 'ar');
        } else {
            return aText.localeCompare(bText, 'ar');
        }
    });
    
    // إضافة علامة الترتيب الجديدة
    header.classList.add(isAscending ? 'sort-desc' : 'sort-asc');
    
    // إعادة ترتيب الصفوف في الجدول
    rows.forEach(row => tbody.appendChild(row));
}

/**
 * تهيئة النوافذ المنبثقة
 */
function initializeModals() {
    // إعادة تعيين النماذج عند إغلاق النوافذ المنبثقة
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('hidden.bs.modal', function() {
            const form = this.querySelector('form');
            if (form) {
                form.reset();
                form.querySelectorAll('.is-valid, .is-invalid').forEach(field => {
                    field.classList.remove('is-valid', 'is-invalid');
                });
                form.querySelectorAll('.invalid-feedback').forEach(feedback => {
                    feedback.remove();
                });
            }
        });
    });
}

/**
 * تهيئة مراقبة الجلسة
 */
function initializeSessionMonitoring() {
    let lastActivity = Date.now();
    
    // تحديث وقت آخر نشاط
    document.addEventListener('click', updateLastActivity);
    document.addEventListener('keypress', updateLastActivity);
    document.addEventListener('scroll', updateLastActivity);
    
    function updateLastActivity() {
        lastActivity = Date.now();
    }
    
    // فحص انتهاء صلاحية الجلسة كل دقيقة
    setInterval(() => {
        const timeSinceLastActivity = Date.now() - lastActivity;
        const timeUntilExpiry = CONFIG.SESSION_TIMEOUT - timeSinceLastActivity;
        
        // تحذير قبل انتهاء الجلسة بـ 5 دقائق
        if (timeUntilExpiry <= 300000 && timeUntilExpiry > 240000) {
            showAlert('ستنتهي صلاحية جلستك خلال 5 دقائق', 'warning', 0);
        }
        
        // إعادة توجيه عند انتهاء الجلسة
        if (timeUntilExpiry <= 0) {
            showAlert('انتهت صلاحية الجلسة. سيتم إعادة توجيهك...', 'danger');
            setTimeout(() => {
                window.location.href = 'logout.php';
            }, 2000);
        }
    }, 60000);
}

/**
 * تهيئة التأثيرات البصرية
 */
function initializeAnimations() {
    // إضافة تأثير الظهور التدريجي للعناصر
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // مراقبة البطاقات والجداول
    document.querySelectorAll('.card, .table-responsive').forEach(el => {
        observer.observe(el);
    });
}

/**
 * حفظ تلقائي للنموذج
 */
function autoSaveForm(form) {
    const formData = new FormData(form);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    // حفظ في التخزين المحلي
    localStorage.setItem(`autosave_${form.id}`, JSON.stringify(data));
    
    // عرض مؤشر الحفظ
    showAutoSaveIndicator();
}

/**
 * عرض مؤشر الحفظ التلقائي
 */
function showAutoSaveIndicator() {
    let indicator = document.getElementById('autosave-indicator');
    if (!indicator) {
        indicator = document.createElement('div');
        indicator.id = 'autosave-indicator';
        indicator.className = 'position-fixed bottom-0 end-0 m-3 badge bg-success';
        indicator.innerHTML = '<i class="fas fa-save me-1"></i>تم الحفظ تلقائياً';
        document.body.appendChild(indicator);
    }
    
    indicator.style.display = 'block';
    setTimeout(() => {
        indicator.style.display = 'none';
    }, 2000);
}

/**
 * استعادة البيانات المحفوظة تلقائياً
 */
function restoreAutoSavedData(formId) {
    const savedData = localStorage.getItem(`autosave_${formId}`);
    if (savedData) {
        const data = JSON.parse(savedData);
        const form = document.getElementById(formId);
        
        if (form) {
            Object.keys(data).forEach(key => {
                const field = form.querySelector(`[name="${key}"]`);
                if (field && field.type !== 'file') {
                    field.value = data[key];
                }
            });
            
            showAlert('تم استعادة البيانات المحفوظة تلقائياً', 'info');
        }
    }
}

/**
 * تنظيف البيانات المحفوظة تلقائياً
 */
function clearAutoSavedData(formId) {
    localStorage.removeItem(`autosave_${formId}`);
}

/**
 * دالة مساعدة لتأكيد الحذف
 */
function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
    return confirm(message);
}

/**
 * دالة مساعدة لتنسيق التاريخ
 */
function formatDate(date, format = 'YYYY-MM-DD') {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    
    return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes);
}

/**
 * دالة مساعدة لتنسيق الأرقام
 */
function formatNumber(number, decimals = 2) {
    return new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(number);
}

// تصدير الدوال للاستخدام العام
window.ElzamLMS = {
    showAlert,
    validateForm,
    confirmDelete,
    formatDate,
    formatNumber,
    restoreAutoSavedData,
    clearAutoSavedData
};
