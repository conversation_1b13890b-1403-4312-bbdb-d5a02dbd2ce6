<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول المشرف
requireAdminLogin();

// فلترة السجلات
$filter_type = $_GET['type'] ?? 'all';
$filter_date = $_GET['date'] ?? '';
$search = $_GET['search'] ?? '';

$where_conditions = [];
$params = [];

if ($filter_type !== 'all') {
    $where_conditions[] = "activity_type LIKE ?";
    $params[] = "%$filter_type%";
}

if ($filter_date) {
    $where_conditions[] = "DATE(timestamp) = ?";
    $params[] = $filter_date;
}

if ($search) {
    $where_conditions[] = "(activity_type LIKE ? OR description LIKE ? OR s.full_name LIKE ? OR a.full_name LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// جلب السجلات مع معلومات المستخدمين
try {
    $stmt = $pdo->prepare("
        SELECT l.*, s.full_name as student_name, a.full_name as admin_name 
        FROM logs l 
        LEFT JOIN students s ON l.student_id = s.id 
        LEFT JOIN admins a ON l.admin_id = a.id 
        $where_clause
        ORDER BY l.timestamp DESC 
        LIMIT 100
    ");
    $stmt->execute($params);
    $logs = $stmt->fetchAll();
    
    // إحصائيات السجلات
    $stmt = $pdo->query("SELECT COUNT(*) as total_logs FROM logs");
    $total_logs = $stmt->fetch()['total_logs'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as today_logs FROM logs WHERE DATE(timestamp) = CURDATE()");
    $today_logs = $stmt->fetch()['today_logs'];
    
    // أنواع الأنشطة الأكثر شيوعاً
    $stmt = $pdo->query("
        SELECT activity_type, COUNT(*) as count 
        FROM logs 
        WHERE DATE(timestamp) >= DATE_SUB(CURDATE(), INTERVAL 7 DAYS)
        GROUP BY activity_type 
        ORDER BY count DESC 
        LIMIT 5
    ");
    $top_activities = $stmt->fetchAll();
    
} catch (PDOException $e) {
    error_log("خطأ في جلب السجلات: " . $e->getMessage());
    $logs = [];
    $total_logs = $today_logs = 0;
    $top_activities = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل الأنشطة - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 10px;
            margin: 5px 0;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover {
            background-color: rgba(255,255,255,0.2);
            color: white;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .activity-item {
            border-left: 4px solid #667eea;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
            border-radius: 0 10px 10px 0;
            transition: all 0.3s ease;
        }
        .activity-item:hover {
            background-color: #f8f9ff;
            transform: translateX(5px);
        }
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }
        .activity-login { background-color: #28a745; }
        .activity-register { background-color: #17a2b8; }
        .activity-payment { background-color: #ffc107; }
        .activity-course { background-color: #6f42c1; }
        .activity-admin { background-color: #dc3545; }
        .activity-default { background-color: #6c757d; }
        .filter-tabs .nav-link {
            border-radius: 10px;
            margin: 0 5px;
            font-weight: 600;
        }
        .filter-tabs .nav-link.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
        }
        .stat-card {
            background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark) 100%);
            color: white;
        }
        .stat-card.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .stat-card.info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <i class="fas fa-shield-alt fa-3x mb-2"></i>
                    <h5 class="fw-bold">لوحة الإدارة</h5>
                </div>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة التحكم
                    </a>
                    <a class="nav-link" href="students.php">
                        <i class="fas fa-users me-2"></i>
                        إدارة الطلاب
                    </a>
                    <a class="nav-link" href="courses.php">
                        <i class="fas fa-book me-2"></i>
                        إدارة الدورات
                    </a>
                    <a class="nav-link" href="enrollments.php">
                        <i class="fas fa-clipboard-list me-2"></i>
                        التسجيلات
                    </a>
                    <a class="nav-link" href="payments.php">
                        <i class="fas fa-credit-card me-2"></i>
                        المدفوعات
                    </a>
                    <a class="nav-link" href="logs.php" style="background-color: rgba(255,255,255,0.2); color: white;">
                        <i class="fas fa-history me-2"></i>
                        سجل الأنشطة
                    </a>
                    <hr class="my-3">
                    <a class="nav-link text-warning" href="../logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>
                        تسجيل الخروج
                    </a>
                </nav>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="fw-bold">سجل الأنشطة</h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.php">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active">سجل الأنشطة</li>
                        </ol>
                    </nav>
                </div>
                
                <!-- Statistics Cards -->
                <div class="row g-4 mb-4">
                    <div class="col-md-4">
                        <div class="card stat-card">
                            <div class="card-body text-center">
                                <i class="fas fa-list fa-2x mb-2"></i>
                                <h4 class="fw-bold"><?php echo $total_logs; ?></h4>
                                <p class="mb-0">إجمالي السجلات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card stat-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar-day fa-2x mb-2"></i>
                                <h4 class="fw-bold"><?php echo $today_logs; ?></h4>
                                <p class="mb-0">أنشطة اليوم</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card stat-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                <h4 class="fw-bold"><?php echo count($top_activities); ?></h4>
                                <p class="mb-0">أنواع الأنشطة</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Filters and Search -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3 align-items-end">
                            <div class="col-md-3">
                                <label for="type" class="form-label">نوع النشاط</label>
                                <select class="form-select" id="type" name="type">
                                    <option value="all" <?php echo $filter_type === 'all' ? 'selected' : ''; ?>>جميع الأنشطة</option>
                                    <option value="دخول" <?php echo $filter_type === 'دخول' ? 'selected' : ''; ?>>تسجيل دخول</option>
                                    <option value="تسجيل" <?php echo $filter_type === 'تسجيل' ? 'selected' : ''; ?>>تسجيل جديد</option>
                                    <option value="دفع" <?php echo $filter_type === 'دفع' ? 'selected' : ''; ?>>عمليات دفع</option>
                                    <option value="دورة" <?php echo $filter_type === 'دورة' ? 'selected' : ''; ?>>إدارة دورات</option>
                                    <option value="مشرف" <?php echo $filter_type === 'مشرف' ? 'selected' : ''; ?>>أنشطة المشرف</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="date" class="form-label">التاريخ</label>
                                <input type="date" class="form-control" id="date" name="date" 
                                       value="<?php echo htmlspecialchars($filter_date); ?>">
                            </div>
                            <div class="col-md-4">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       placeholder="البحث في الأنشطة..." 
                                       value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-1"></i>
                                    بحث
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="row g-4">
                    <!-- Activity Logs -->
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="fw-bold mb-0">
                                    <i class="fas fa-history me-2"></i>
                                    سجل الأنشطة (<?php echo count($logs); ?>)
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($logs)): ?>
                                    <div class="text-center py-5">
                                        <i class="fas fa-history fa-4x text-muted mb-3"></i>
                                        <h4 class="text-muted">لا توجد أنشطة</h4>
                                        <p class="text-muted">لا توجد أنشطة تطابق معايير البحث</p>
                                    </div>
                                <?php else: ?>
                                    <div style="max-height: 600px; overflow-y: auto;">
                                        <?php foreach ($logs as $log): ?>
                                            <div class="activity-item">
                                                <div class="d-flex align-items-start">
                                                    <div class="activity-icon activity-<?php echo getActivityClass($log['activity_type']); ?> me-3">
                                                        <i class="fas fa-<?php echo getActivityIcon($log['activity_type']); ?>"></i>
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <div class="d-flex justify-content-between align-items-start">
                                                            <div>
                                                                <h6 class="fw-bold mb-1"><?php echo htmlspecialchars($log['activity_type']); ?></h6>
                                                                <p class="mb-1 text-muted small">
                                                                    <?php if ($log['student_name']): ?>
                                                                        <i class="fas fa-user me-1"></i>
                                                                        الطالب: <?php echo htmlspecialchars($log['student_name']); ?>
                                                                    <?php elseif ($log['admin_name']): ?>
                                                                        <i class="fas fa-user-shield me-1"></i>
                                                                        المشرف: <?php echo htmlspecialchars($log['admin_name']); ?>
                                                                    <?php else: ?>
                                                                        <i class="fas fa-globe me-1"></i>
                                                                        مستخدم غير معروف
                                                                    <?php endif; ?>
                                                                </p>
                                                                <?php if ($log['description']): ?>
                                                                    <p class="mb-1 small"><?php echo htmlspecialchars($log['description']); ?></p>
                                                                <?php endif; ?>
                                                                <p class="mb-0 text-muted small">
                                                                    <i class="fas fa-map-marker-alt me-1"></i>
                                                                    IP: <?php echo htmlspecialchars($log['ip_address']); ?>
                                                                </p>
                                                            </div>
                                                            <div class="text-end">
                                                                <small class="text-muted">
                                                                    <?php echo formatDate($log['timestamp'], 'Y-m-d'); ?>
                                                                </small>
                                                                <br>
                                                                <small class="text-muted">
                                                                    <?php echo formatDate($log['timestamp'], 'H:i:s'); ?>
                                                                </small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Top Activities -->
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="fw-bold mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>
                                    الأنشطة الأكثر شيوعاً
                                </h5>
                                <small class="text-muted">آخر 7 أيام</small>
                            </div>
                            <div class="card-body">
                                <?php if (empty($top_activities)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد بيانات</p>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($top_activities as $activity): ?>
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="activity-icon activity-<?php echo getActivityClass($activity['activity_type']); ?> me-2" style="width: 30px; height: 30px; font-size: 12px;">
                                                    <i class="fas fa-<?php echo getActivityIcon($activity['activity_type']); ?>"></i>
                                                </div>
                                                <span class="fw-bold"><?php echo htmlspecialchars($activity['activity_type']); ?></span>
                                            </div>
                                            <span class="badge bg-primary"><?php echo $activity['count']; ?></span>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Quick Actions -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="fw-bold mb-0">
                                    <i class="fas fa-tools me-2"></i>
                                    إجراءات سريعة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="?date=<?php echo date('Y-m-d'); ?>" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-calendar-day me-1"></i>
                                        أنشطة اليوم
                                    </a>
                                    <a href="?type=دخول" class="btn btn-outline-success btn-sm">
                                        <i class="fas fa-sign-in-alt me-1"></i>
                                        تسجيلات الدخول
                                    </a>
                                    <a href="?type=دفع" class="btn btn-outline-warning btn-sm">
                                        <i class="fas fa-credit-card me-1"></i>
                                        عمليات الدفع
                                    </a>
                                    <a href="logs.php" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-refresh me-1"></i>
                                        إعادة تعيين الفلاتر
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
/**
 * الحصول على فئة CSS للنشاط
 */
function getActivityClass($activityType) {
    if (strpos($activityType, 'دخول') !== false) return 'login';
    if (strpos($activityType, 'تسجيل') !== false) return 'register';
    if (strpos($activityType, 'دفع') !== false) return 'payment';
    if (strpos($activityType, 'دورة') !== false) return 'course';
    if (strpos($activityType, 'مشرف') !== false) return 'admin';
    return 'default';
}

/**
 * الحصول على أيقونة النشاط
 */
function getActivityIcon($activityType) {
    if (strpos($activityType, 'دخول') !== false) return 'sign-in-alt';
    if (strpos($activityType, 'تسجيل') !== false) return 'user-plus';
    if (strpos($activityType, 'دفع') !== false) return 'credit-card';
    if (strpos($activityType, 'دورة') !== false) return 'book';
    if (strpos($activityType, 'مشرف') !== false) return 'shield-alt';
    if (strpos($activityType, 'خروج') !== false) return 'sign-out-alt';
    return 'circle';
}
?>
