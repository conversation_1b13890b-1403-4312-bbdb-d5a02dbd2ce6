<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول المشرف
requireAdminLogin();

// فلترة المدفوعات
$filter = $_GET['filter'] ?? 'all';
$search = $_GET['search'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';

$where_conditions = [];
$params = [];

if ($filter !== 'all') {
    $where_conditions[] = "p.payment_status = ?";
    $params[] = $filter === 'completed' ? 'مكتمل' : ($filter === 'pending' ? 'معلق' : 'فاشل');
}

if ($search) {
    $where_conditions[] = "(s.full_name LIKE ? OR c.course_name LIKE ? OR p.transaction_id LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

if ($date_from) {
    $where_conditions[] = "DATE(p.payment_date) >= ?";
    $params[] = $date_from;
}

if ($date_to) {
    $where_conditions[] = "DATE(p.payment_date) <= ?";
    $params[] = $date_to;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

try {
    $stmt = $pdo->prepare("
        SELECT p.*, e.enrollment_date, s.full_name as student_name, s.student_id, s.email as student_email,
               c.course_name, c.instructor
        FROM payments p 
        JOIN enrollments e ON p.enrollment_id = e.id
        JOIN students s ON e.student_id = s.id 
        JOIN courses c ON e.course_id = c.id 
        $where_clause
        ORDER BY p.payment_date DESC
    ");
    $stmt->execute($params);
    $payments = $stmt->fetchAll();
    
    // إحصائيات المدفوعات
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM payments");
    $total_payments = $stmt->fetch()['total'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as completed FROM payments WHERE payment_status = 'مكتمل'");
    $completed_payments = $stmt->fetch()['completed'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as pending FROM payments WHERE payment_status = 'معلق'");
    $pending_payments = $stmt->fetch()['pending'];
    
    $stmt = $pdo->query("SELECT SUM(amount) as revenue FROM payments WHERE payment_status = 'مكتمل'");
    $total_revenue = $stmt->fetch()['revenue'] ?? 0;
    
    // إحصائيات شهرية
    $stmt = $pdo->query("
        SELECT MONTH(payment_date) as month, YEAR(payment_date) as year, SUM(amount) as monthly_revenue 
        FROM payments 
        WHERE payment_status = 'مكتمل' AND YEAR(payment_date) = YEAR(CURDATE())
        GROUP BY YEAR(payment_date), MONTH(payment_date)
        ORDER BY year DESC, month DESC
        LIMIT 6
    ");
    $monthly_stats = $stmt->fetchAll();
    
} catch (PDOException $e) {
    error_log("خطأ في جلب المدفوعات: " . $e->getMessage());
    $payments = [];
    $total_payments = $completed_payments = $pending_payments = $total_revenue = 0;
    $monthly_stats = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المدفوعات - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 10px;
            margin: 5px 0;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover {
            background-color: rgba(255,255,255,0.2);
            color: white;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .status-completed { background-color: #28a745; color: #fff; }
        .status-pending { background-color: #ffc107; color: #000; }
        .status-failed { background-color: #dc3545; color: #fff; }
        .stat-card {
            background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark) 100%);
            color: white;
        }
        .stat-card.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .stat-card.warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }
        .stat-card.info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <i class="fas fa-shield-alt fa-3x mb-2"></i>
                    <h5 class="fw-bold">لوحة الإدارة</h5>
                </div>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة التحكم
                    </a>
                    <a class="nav-link" href="students.php">
                        <i class="fas fa-users me-2"></i>
                        إدارة الطلاب
                    </a>
                    <a class="nav-link" href="courses.php">
                        <i class="fas fa-book me-2"></i>
                        إدارة الدورات
                    </a>
                    <a class="nav-link" href="enrollments.php">
                        <i class="fas fa-clipboard-list me-2"></i>
                        التسجيلات
                    </a>
                    <a class="nav-link" href="payments.php" style="background-color: rgba(255,255,255,0.2); color: white;">
                        <i class="fas fa-credit-card me-2"></i>
                        المدفوعات
                    </a>
                    <a class="nav-link" href="logs.php">
                        <i class="fas fa-history me-2"></i>
                        سجل الأنشطة
                    </a>
                    <hr class="my-3">
                    <a class="nav-link text-warning" href="../logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>
                        تسجيل الخروج
                    </a>
                </nav>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="fw-bold">إدارة المدفوعات</h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.php">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active">المدفوعات</li>
                        </ol>
                    </nav>
                </div>
                
                <!-- Statistics Cards -->
                <div class="row g-4 mb-4">
                    <div class="col-md-3">
                        <div class="card stat-card">
                            <div class="card-body text-center">
                                <i class="fas fa-credit-card fa-2x mb-2"></i>
                                <h4 class="fw-bold"><?php echo $total_payments; ?></h4>
                                <p class="mb-0">إجمالي المدفوعات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h4 class="fw-bold"><?php echo $completed_payments; ?></h4>
                                <p class="mb-0">مكتملة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <h4 class="fw-bold"><?php echo $pending_payments; ?></h4>
                                <p class="mb-0">معلقة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-money-bill fa-2x mb-2"></i>
                                <h4 class="fw-bold"><?php echo formatPrice($total_revenue); ?></h4>
                                <p class="mb-0">إجمالي الإيرادات</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Monthly Revenue Chart -->
                <?php if (!empty($monthly_stats)): ?>
                <div class="row g-4 mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="fw-bold mb-0">
                                    <i class="fas fa-chart-line me-2"></i>
                                    الإيرادات الشهرية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php foreach ($monthly_stats as $stat): ?>
                                        <div class="col-md-2 text-center">
                                            <div class="mb-2">
                                                <strong><?php echo $stat['year'] . '/' . str_pad($stat['month'], 2, '0', STR_PAD_LEFT); ?></strong>
                                            </div>
                                            <div class="text-success fw-bold">
                                                <?php echo formatPrice($stat['monthly_revenue']); ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Filters and Search -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3 align-items-end">
                            <div class="col-md-2">
                                <label for="filter" class="form-label">الحالة</label>
                                <select class="form-select" id="filter" name="filter">
                                    <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>الكل</option>
                                    <option value="completed" <?php echo $filter === 'completed' ? 'selected' : ''; ?>>مكتملة</option>
                                    <option value="pending" <?php echo $filter === 'pending' ? 'selected' : ''; ?>>معلقة</option>
                                    <option value="failed" <?php echo $filter === 'failed' ? 'selected' : ''; ?>>فاشلة</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="date_from" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" 
                                       value="<?php echo htmlspecialchars($date_from); ?>">
                            </div>
                            <div class="col-md-2">
                                <label for="date_to" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" 
                                       value="<?php echo htmlspecialchars($date_to); ?>">
                            </div>
                            <div class="col-md-4">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       placeholder="البحث بالطالب أو الدورة أو رقم المعاملة..." 
                                       value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-1"></i>
                                    بحث
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Payments Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="fw-bold mb-0">
                            <i class="fas fa-credit-card me-2"></i>
                            قائمة المدفوعات (<?php echo count($payments); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($payments)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-credit-card fa-4x text-muted mb-3"></i>
                                <h4 class="text-muted">لا توجد مدفوعات</h4>
                                <p class="text-muted">لا توجد مدفوعات تطابق معايير البحث</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>رقم المعاملة</th>
                                            <th>معلومات الطالب</th>
                                            <th>الدورة</th>
                                            <th>المبلغ</th>
                                            <th>طريقة الدفع</th>
                                            <th>تاريخ الدفع</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($payments as $payment): ?>
                                            <tr>
                                                <td>
                                                    <code><?php echo htmlspecialchars($payment['transaction_id'] ?: 'غير محدد'); ?></code>
                                                </td>
                                                <td>
                                                    <div>
                                                        <h6 class="fw-bold mb-1"><?php echo htmlspecialchars($payment['student_name']); ?></h6>
                                                        <small class="text-muted"><?php echo htmlspecialchars($payment['student_email']); ?></small><br>
                                                        <small class="text-primary">رقم: <?php echo htmlspecialchars($payment['student_id']); ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        <h6 class="fw-bold mb-1"><?php echo htmlspecialchars($payment['course_name']); ?></h6>
                                                        <small class="text-muted">المدرب: <?php echo htmlspecialchars($payment['instructor']); ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="fw-bold text-success"><?php echo formatPrice($payment['amount']); ?></span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary"><?php echo htmlspecialchars($payment['payment_method']); ?></span>
                                                </td>
                                                <td>
                                                    <small><?php echo formatDate($payment['payment_date'], 'Y-m-d H:i'); ?></small>
                                                </td>
                                                <td>
                                                    <span class="status-badge status-<?php echo $payment['payment_status'] === 'مكتمل' ? 'completed' : ($payment['payment_status'] === 'فاشل' ? 'failed' : 'pending'); ?>">
                                                        <?php echo $payment['payment_status']; ?>
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
