<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول المشرف
requireAdminLogin();

$message = '';
$message_type = '';

// معالجة تحديث حالة الطالب
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $student_id = (int)$_POST['student_id'];
    $action = $_POST['action'];
    
    try {
        if ($action === 'approve') {
            $stmt = $pdo->prepare("UPDATE students SET status = 'مقبول' WHERE id = ?");
            $stmt->execute([$student_id]);
            
            // إرسال إشعار للطالب
            $student = getStudentById($student_id);
            if ($student) {
                $subject = 'تم قبول طلب التسجيل - منصة الزام';
                $message_content = "
                    <h3>مرحباً {$student['full_name']}</h3>
                    <p>نحن سعداء لإبلاغك بأنه تم قبول طلب التسجيل الخاص بك في منصة الزام التعليمية.</p>
                    <p>يمكنك الآن تسجيل الدخول والتسجيل في الدورات المتاحة.</p>
                    <p>مع أطيب التحيات،<br>فريق منصة الزام</p>
                ";
                sendNotification($student['email'], $subject, $message_content);
            }
            
            logActivity(null, $_SESSION['admin_id'], 'قبول طالب', "تم قبول الطالب رقم: $student_id");
            $message = 'تم قبول الطالب بنجاح';
            $message_type = 'success';
            
        } elseif ($action === 'reject') {
            $stmt = $pdo->prepare("UPDATE students SET status = 'مرفوض' WHERE id = ?");
            $stmt->execute([$student_id]);
            
            // إرسال إشعار للطالب
            $student = getStudentById($student_id);
            if ($student) {
                $subject = 'تحديث حالة طلب التسجيل - منصة الزام';
                $message_content = "
                    <h3>مرحباً {$student['full_name']}</h3>
                    <p>نأسف لإبلاغك بأنه لم يتم قبول طلب التسجيل الخاص بك في منصة الزام التعليمية.</p>
                    <p>يرجى التواصل معنا لمزيد من المعلومات.</p>
                    <p>مع أطيب التحيات،<br>فريق منصة الزام</p>
                ";
                sendNotification($student['email'], $subject, $message_content);
            }
            
            logActivity(null, $_SESSION['admin_id'], 'رفض طالب', "تم رفض الطالب رقم: $student_id");
            $message = 'تم رفض الطالب';
            $message_type = 'warning';
            
        } elseif ($action === 'pending') {
            $stmt = $pdo->prepare("UPDATE students SET status = 'معلق' WHERE id = ?");
            $stmt->execute([$student_id]);
            
            logActivity(null, $_SESSION['admin_id'], 'تعليق طالب', "تم تعليق الطالب رقم: $student_id");
            $message = 'تم تعليق حالة الطالب';
            $message_type = 'info';
        }
    } catch (PDOException $e) {
        error_log("خطأ في تحديث حالة الطالب: " . $e->getMessage());
        $message = 'حدث خطأ في تحديث حالة الطالب';
        $message_type = 'danger';
    }
}

// فلترة الطلاب
$filter = $_GET['filter'] ?? 'all';
$search = $_GET['search'] ?? '';

$where_conditions = [];
$params = [];

if ($filter !== 'all') {
    $where_conditions[] = "status = ?";
    $params[] = $filter === 'pending' ? 'معلق' : ($filter === 'approved' ? 'مقبول' : 'مرفوض');
}

if ($search) {
    $where_conditions[] = "(full_name LIKE ? OR email LIKE ? OR student_id LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

try {
    $stmt = $pdo->prepare("SELECT * FROM students $where_clause ORDER BY created_at DESC");
    $stmt->execute($params);
    $students = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("خطأ في جلب الطلاب: " . $e->getMessage());
    $students = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الطلاب - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 10px;
            margin: 5px 0;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover {
            background-color: rgba(255,255,255,0.2);
            color: white;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .status-pending { background-color: #ffc107; color: #000; }
        .status-approved { background-color: #28a745; color: #fff; }
        .status-rejected { background-color: #dc3545; color: #fff; }
        .student-image {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 10px;
        }
        .filter-tabs .nav-link {
            border-radius: 10px;
            margin: 0 5px;
            font-weight: 600;
        }
        .filter-tabs .nav-link.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <i class="fas fa-shield-alt fa-3x mb-2"></i>
                    <h5 class="fw-bold">لوحة الإدارة</h5>
                </div>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة التحكم
                    </a>
                    <a class="nav-link" href="students.php" style="background-color: rgba(255,255,255,0.2); color: white;">
                        <i class="fas fa-users me-2"></i>
                        إدارة الطلاب
                    </a>
                    <a class="nav-link" href="courses.php">
                        <i class="fas fa-book me-2"></i>
                        إدارة الدورات
                    </a>
                    <a class="nav-link" href="enrollments.php">
                        <i class="fas fa-clipboard-list me-2"></i>
                        التسجيلات
                    </a>
                    <a class="nav-link" href="payments.php">
                        <i class="fas fa-credit-card me-2"></i>
                        المدفوعات
                    </a>
                    <a class="nav-link" href="logs.php">
                        <i class="fas fa-history me-2"></i>
                        سجل الأنشطة
                    </a>
                    <hr class="my-3">
                    <a class="nav-link text-warning" href="../logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>
                        تسجيل الخروج
                    </a>
                </nav>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="fw-bold">إدارة الطلاب</h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.php">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active">إدارة الطلاب</li>
                        </ol>
                    </nav>
                </div>
                
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Filters and Search -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row g-3 align-items-center">
                            <div class="col-md-6">
                                <!-- Filter Tabs -->
                                <ul class="nav nav-pills filter-tabs" role="tablist">
                                    <li class="nav-item">
                                        <a class="nav-link <?php echo $filter === 'all' ? 'active' : ''; ?>" 
                                           href="?filter=all&search=<?php echo urlencode($search); ?>">
                                            الكل
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link <?php echo $filter === 'pending' ? 'active' : ''; ?>" 
                                           href="?filter=pending&search=<?php echo urlencode($search); ?>">
                                            معلق
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link <?php echo $filter === 'approved' ? 'active' : ''; ?>" 
                                           href="?filter=approved&search=<?php echo urlencode($search); ?>">
                                            مقبول
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link <?php echo $filter === 'rejected' ? 'active' : ''; ?>" 
                                           href="?filter=rejected&search=<?php echo urlencode($search); ?>">
                                            مرفوض
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <!-- Search -->
                                <form method="GET" class="d-flex">
                                    <input type="hidden" name="filter" value="<?php echo htmlspecialchars($filter); ?>">
                                    <input type="text" class="form-control me-2" name="search" 
                                           placeholder="البحث بالاسم، البريد الإلكتروني، أو الرقم الجامعي..." 
                                           value="<?php echo htmlspecialchars($search); ?>">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Students Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="fw-bold mb-0">
                            <i class="fas fa-users me-2"></i>
                            قائمة الطلاب (<?php echo count($students); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($students)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-4x text-muted mb-3"></i>
                                <h4 class="text-muted">لا يوجد طلاب</h4>
                                <p class="text-muted">لا توجد نتائج تطابق معايير البحث</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>البطاقة</th>
                                            <th>معلومات الطالب</th>
                                            <th>المرحلة والتخصص</th>
                                            <th>تاريخ التسجيل</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($students as $student): ?>
                                            <tr>
                                                <td>
                                                    <?php if ($student['university_card']): ?>
                                                        <img src="../uploads/university_cards/<?php echo $student['university_card']; ?>" 
                                                             class="student-image" alt="البطاقة الجامعية"
                                                             data-bs-toggle="modal" data-bs-target="#imageModal"
                                                             data-image="../uploads/university_cards/<?php echo $student['university_card']; ?>"
                                                             style="cursor: pointer;">
                                                    <?php else: ?>
                                                        <div class="student-image bg-light d-flex align-items-center justify-content-center">
                                                            <i class="fas fa-id-card text-muted"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div>
                                                        <h6 class="fw-bold mb-1"><?php echo htmlspecialchars($student['full_name']); ?></h6>
                                                        <small class="text-muted"><?php echo htmlspecialchars($student['email']); ?></small><br>
                                                        <small class="text-primary">رقم جامعي: <?php echo htmlspecialchars($student['student_id']); ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        <span class="fw-bold"><?php echo htmlspecialchars($student['university_stage']); ?></span><br>
                                                        <small class="text-muted"><?php echo htmlspecialchars($student['major']); ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <small><?php echo formatDate($student['created_at'], 'Y-m-d H:i'); ?></small>
                                                </td>
                                                <td>
                                                    <span class="status-badge status-<?php echo $student['status'] === 'مقبول' ? 'approved' : ($student['status'] === 'مرفوض' ? 'rejected' : 'pending'); ?>">
                                                        <?php echo $student['status']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <?php if ($student['status'] !== 'مقبول'): ?>
                                                            <form method="POST" class="d-inline">
                                                                <input type="hidden" name="student_id" value="<?php echo $student['id']; ?>">
                                                                <input type="hidden" name="action" value="approve">
                                                                <button type="submit" class="btn btn-sm btn-success" 
                                                                        onclick="return confirm('هل أنت متأكد من قبول هذا الطالب؟')">
                                                                    <i class="fas fa-check"></i>
                                                                </button>
                                                            </form>
                                                        <?php endif; ?>
                                                        
                                                        <?php if ($student['status'] !== 'مرفوض'): ?>
                                                            <form method="POST" class="d-inline">
                                                                <input type="hidden" name="student_id" value="<?php echo $student['id']; ?>">
                                                                <input type="hidden" name="action" value="reject">
                                                                <button type="submit" class="btn btn-sm btn-danger" 
                                                                        onclick="return confirm('هل أنت متأكد من رفض هذا الطالب؟')">
                                                                    <i class="fas fa-times"></i>
                                                                </button>
                                                            </form>
                                                        <?php endif; ?>
                                                        
                                                        <?php if ($student['status'] !== 'معلق'): ?>
                                                            <form method="POST" class="d-inline">
                                                                <input type="hidden" name="student_id" value="<?php echo $student['id']; ?>">
                                                                <input type="hidden" name="action" value="pending">
                                                                <button type="submit" class="btn btn-sm btn-warning" 
                                                                        onclick="return confirm('هل أنت متأكد من تعليق هذا الطالب؟')">
                                                                    <i class="fas fa-clock"></i>
                                                                </button>
                                                            </form>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Modal -->
    <div class="modal fade" id="imageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">البطاقة الجامعية</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" class="img-fluid" alt="البطاقة الجامعية">
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // عرض الصورة في النافذة المنبثقة
        document.addEventListener('DOMContentLoaded', function() {
            const imageModal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            
            imageModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const imageSrc = button.getAttribute('data-image');
                modalImage.src = imageSrc;
            });
        });
    </script>
</body>
</html>
