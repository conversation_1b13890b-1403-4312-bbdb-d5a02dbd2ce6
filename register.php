<?php
require_once 'config/config.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// إعادة توجيه إذا كان المستخدم مسجل دخول
if (isStudentLoggedIn() || isAdminLoggedIn()) {
    redirectUser();
}

$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من رفع الملف
    if (!isset($_FILES['university_card']) || $_FILES['university_card']['error'] !== UPLOAD_ERR_OK) {
        $message = 'يرجى رفع صورة البطاقة الجامعية';
        $message_type = 'danger';
    } else {
        // رفع صورة البطاقة الجامعية
        $upload_result = uploadImage($_FILES['university_card'], UNIVERSITY_CARDS_PATH);
        
        if ($upload_result['success']) {
            // تحضير البيانات للتسجيل
            $student_data = [
                'student_id' => sanitizeInput($_POST['student_id']),
                'full_name' => sanitizeInput($_POST['full_name']),
                'university_stage' => sanitizeInput($_POST['university_stage']),
                'major' => sanitizeInput($_POST['major']),
                'university_card' => $upload_result['filename'],
                'email' => sanitizeInput($_POST['email']),
                'password' => $_POST['password']
            ];
            
            // تسجيل الطالب
            $result = registerStudent($student_data);
            
            if ($result['success']) {
                $message = getMessage('success', 'registration');
                $message_type = 'success';
                
                // إرسال إشعار للمشرف (اختياري)
                $notification_subject = 'طلب تسجيل جديد - منصة الزام';
                $notification_message = "
                    <h3>طلب تسجيل جديد</h3>
                    <p><strong>الاسم:</strong> {$student_data['full_name']}</p>
                    <p><strong>الرقم الجامعي:</strong> {$student_data['student_id']}</p>
                    <p><strong>البريد الإلكتروني:</strong> {$student_data['email']}</p>
                    <p><strong>المرحلة الجامعية:</strong> {$student_data['university_stage']}</p>
                    <p><strong>التخصص:</strong> {$student_data['major']}</p>
                    <p>يرجى مراجعة الطلب من لوحة الإدارة.</p>
                ";
                sendNotification(ADMIN_EMAIL, $notification_subject, $notification_message);
                
            } else {
                $message = $result['message'];
                $message_type = 'danger';
            }
        } else {
            $message = $upload_result['message'];
            $message_type = 'danger';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل طالب جديد - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        .btn-custom {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: bold;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .form-label {
            font-weight: 600;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-white text-center py-4">
                        <h3 class="fw-bold text-primary mb-0">
                            <i class="fas fa-user-plus me-2"></i>
                            تسجيل طالب جديد
                        </h3>
                        <p class="text-muted mt-2">املأ البيانات التالية للتسجيل في المنصة</p>
                    </div>
                    <div class="card-body p-5">
                        <?php if ($message): ?>
                            <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                                <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                                <?php echo $message; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <form method="POST" enctype="multipart/form-data" id="registerForm">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="student_id" class="form-label">
                                        <i class="fas fa-id-card me-1"></i>
                                        الرقم الجامعي *
                                    </label>
                                    <input type="text" class="form-control" id="student_id" name="student_id" 
                                           placeholder="أدخل الرقم الجامعي" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="full_name" class="form-label">
                                        <i class="fas fa-user me-1"></i>
                                        الاسم الكامل *
                                    </label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" 
                                           placeholder="أدخل الاسم الكامل" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="university_stage" class="form-label">
                                        <i class="fas fa-graduation-cap me-1"></i>
                                        المرحلة الجامعية *
                                    </label>
                                    <select class="form-select" id="university_stage" name="university_stage" required>
                                        <option value="">اختر المرحلة الجامعية</option>
                                        <option value="سنة أولى">سنة أولى</option>
                                        <option value="سنة ثانية">سنة ثانية</option>
                                        <option value="سنة ثالثة">سنة ثالثة</option>
                                        <option value="سنة رابعة">سنة رابعة</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="major" class="form-label">
                                        <i class="fas fa-book me-1"></i>
                                        التخصص العلمي *
                                    </label>
                                    <input type="text" class="form-control" id="major" name="major" 
                                           placeholder="أدخل التخصص العلمي" required>
                                </div>
                                <div class="col-12">
                                    <label for="university_card" class="form-label">
                                        <i class="fas fa-image me-1"></i>
                                        صورة البطاقة الجامعية *
                                    </label>
                                    <input type="file" class="form-control" id="university_card" name="university_card" 
                                           accept="image/jpeg,image/png,image/jpg" required>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        الحد الأقصى لحجم الملف: 2 ميجابايت. الأنواع المدعومة: JPG, PNG
                                    </div>
                                </div>
                                <div class="col-12">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope me-1"></i>
                                        البريد الإلكتروني *
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           placeholder="أدخل البريد الإلكتروني" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-1"></i>
                                        كلمة المرور *
                                    </label>
                                    <input type="password" class="form-control" id="password" name="password" 
                                           placeholder="أدخل كلمة المرور" required minlength="8">
                                </div>
                                <div class="col-md-6">
                                    <label for="confirm_password" class="form-label">
                                        <i class="fas fa-lock me-1"></i>
                                        تأكيد كلمة المرور *
                                    </label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                           placeholder="أعد إدخال كلمة المرور" required>
                                </div>
                            </div>

                            <div class="d-grid gap-2 mt-4">
                                <button type="submit" class="btn btn-custom btn-lg">
                                    <i class="fas fa-user-plus me-2"></i>
                                    تسجيل الحساب
                                </button>
                            </div>
                        </form>

                        <div class="text-center mt-4">
                            <p class="text-muted">
                                لديك حساب بالفعل؟ 
                                <a href="login.php" class="text-primary text-decoration-none fw-bold">
                                    سجل الدخول
                                </a>
                            </p>
                            <a href="index.php" class="text-muted text-decoration-none">
                                <i class="fas fa-arrow-right me-1"></i>
                                العودة للصفحة الرئيسية
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // التحقق من تطابق كلمات المرور
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('كلمات المرور غير متطابقة!');
                return false;
            }
            
            if (password.length < 8) {
                e.preventDefault();
                alert('كلمة المرور يجب أن تكون 8 أحرف على الأقل!');
                return false;
            }
        });

        // التحقق من حجم الملف
        document.getElementById('university_card').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file && file.size > <?php echo MAX_FILE_SIZE; ?>) {
                alert('حجم الملف كبير جداً! الحد الأقصى 2 ميجابايت.');
                e.target.value = '';
            }
        });
    </script>
</body>
</html>
