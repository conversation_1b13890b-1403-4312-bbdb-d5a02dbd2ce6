<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول المشرف
requireAdminLogin();

$message = '';
$message_type = '';

// معالجة تحديث حالة التسجيل
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $enrollment_id = (int)$_POST['enrollment_id'];
    $action = $_POST['action'];
    
    try {
        if ($action === 'approve_payment') {
            $stmt = $pdo->prepare("UPDATE enrollments SET payment_status = 'مدفوع' WHERE id = ?");
            $stmt->execute([$enrollment_id]);
            
            logActivity(null, $_SESSION['admin_id'], 'موافقة دفع', "تم قبول دفع التسجيل رقم: $enrollment_id");
            $message = 'تم قبول الدفع بنجاح';
            $message_type = 'success';
            
        } elseif ($action === 'reject_payment') {
            $stmt = $pdo->prepare("UPDATE enrollments SET payment_status = 'مرفوض' WHERE id = ?");
            $stmt->execute([$enrollment_id]);
            
            logActivity(null, $_SESSION['admin_id'], 'رفض دفع', "تم رفض دفع التسجيل رقم: $enrollment_id");
            $message = 'تم رفض الدفع';
            $message_type = 'warning';
            
        } elseif ($action === 'enable_access') {
            $stmt = $pdo->prepare("UPDATE enrollments SET course_access = 'متاح' WHERE id = ?");
            $stmt->execute([$enrollment_id]);
            
            logActivity(null, $_SESSION['admin_id'], 'تفعيل وصول', "تم تفعيل الوصول للدورة رقم: $enrollment_id");
            $message = 'تم تفعيل الوصول للدورة';
            $message_type = 'success';
            
        } elseif ($action === 'disable_access') {
            $stmt = $pdo->prepare("UPDATE enrollments SET course_access = 'غير متاح' WHERE id = ?");
            $stmt->execute([$enrollment_id]);
            
            logActivity(null, $_SESSION['admin_id'], 'إلغاء وصول', "تم إلغاء الوصول للدورة رقم: $enrollment_id");
            $message = 'تم إلغاء الوصول للدورة';
            $message_type = 'info';
        }
    } catch (PDOException $e) {
        error_log("خطأ في تحديث التسجيل: " . $e->getMessage());
        $message = 'حدث خطأ في تحديث التسجيل';
        $message_type = 'danger';
    }
}

// فلترة التسجيلات
$filter = $_GET['filter'] ?? 'all';
$search = $_GET['search'] ?? '';

$where_conditions = [];
$params = [];

if ($filter !== 'all') {
    if ($filter === 'paid') {
        $where_conditions[] = "e.payment_status = 'مدفوع'";
    } elseif ($filter === 'pending') {
        $where_conditions[] = "e.payment_status = 'معلق'";
    } elseif ($filter === 'accessible') {
        $where_conditions[] = "e.course_access = 'متاح'";
    }
}

if ($search) {
    $where_conditions[] = "(s.full_name LIKE ? OR c.course_name LIKE ? OR s.student_id LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

try {
    $stmt = $pdo->prepare("
        SELECT e.*, s.full_name as student_name, s.student_id, s.email as student_email,
               c.course_name, c.instructor, c.price, c.expected_start_date
        FROM enrollments e 
        JOIN students s ON e.student_id = s.id 
        JOIN courses c ON e.course_id = c.id 
        $where_clause
        ORDER BY e.enrollment_date DESC
    ");
    $stmt->execute($params);
    $enrollments = $stmt->fetchAll();
    
    // إحصائيات التسجيلات
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM enrollments");
    $total_enrollments = $stmt->fetch()['total'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as paid FROM enrollments WHERE payment_status = 'مدفوع'");
    $paid_enrollments = $stmt->fetch()['paid'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as pending FROM enrollments WHERE payment_status = 'معلق'");
    $pending_enrollments = $stmt->fetch()['pending'];
    
    $stmt = $pdo->query("SELECT SUM(c.price) as revenue FROM enrollments e JOIN courses c ON e.course_id = c.id WHERE e.payment_status = 'مدفوع'");
    $total_revenue = $stmt->fetch()['revenue'] ?? 0;
    
} catch (PDOException $e) {
    error_log("خطأ في جلب التسجيلات: " . $e->getMessage());
    $enrollments = [];
    $total_enrollments = $paid_enrollments = $pending_enrollments = $total_revenue = 0;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة التسجيلات - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 10px;
            margin: 5px 0;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover {
            background-color: rgba(255,255,255,0.2);
            color: white;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .status-pending { background-color: #ffc107; color: #000; }
        .status-paid { background-color: #28a745; color: #fff; }
        .status-rejected { background-color: #dc3545; color: #fff; }
        .access-available { background-color: #28a745; color: #fff; }
        .access-unavailable { background-color: #6c757d; color: #fff; }
        .stat-card {
            background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark) 100%);
            color: white;
        }
        .stat-card.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .stat-card.warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }
        .stat-card.info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <i class="fas fa-shield-alt fa-3x mb-2"></i>
                    <h5 class="fw-bold">لوحة الإدارة</h5>
                </div>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة التحكم
                    </a>
                    <a class="nav-link" href="students.php">
                        <i class="fas fa-users me-2"></i>
                        إدارة الطلاب
                    </a>
                    <a class="nav-link" href="courses.php">
                        <i class="fas fa-book me-2"></i>
                        إدارة الدورات
                    </a>
                    <a class="nav-link" href="enrollments.php" style="background-color: rgba(255,255,255,0.2); color: white;">
                        <i class="fas fa-clipboard-list me-2"></i>
                        التسجيلات
                    </a>
                    <a class="nav-link" href="payments.php">
                        <i class="fas fa-credit-card me-2"></i>
                        المدفوعات
                    </a>
                    <a class="nav-link" href="logs.php">
                        <i class="fas fa-history me-2"></i>
                        سجل الأنشطة
                    </a>
                    <hr class="my-3">
                    <a class="nav-link text-warning" href="../logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>
                        تسجيل الخروج
                    </a>
                </nav>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="fw-bold">إدارة التسجيلات</h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.php">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active">التسجيلات</li>
                        </ol>
                    </nav>
                </div>
                
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Statistics Cards -->
                <div class="row g-4 mb-4">
                    <div class="col-md-3">
                        <div class="card stat-card">
                            <div class="card-body text-center">
                                <i class="fas fa-clipboard-list fa-2x mb-2"></i>
                                <h4 class="fw-bold"><?php echo $total_enrollments; ?></h4>
                                <p class="mb-0">إجمالي التسجيلات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h4 class="fw-bold"><?php echo $paid_enrollments; ?></h4>
                                <p class="mb-0">مدفوعة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <h4 class="fw-bold"><?php echo $pending_enrollments; ?></h4>
                                <p class="mb-0">معلقة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-money-bill fa-2x mb-2"></i>
                                <h4 class="fw-bold"><?php echo formatPrice($total_revenue); ?></h4>
                                <p class="mb-0">إجمالي الإيرادات</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Filters and Search -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3 align-items-end">
                            <div class="col-md-4">
                                <label for="filter" class="form-label">فلترة التسجيلات</label>
                                <select class="form-select" id="filter" name="filter">
                                    <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>جميع التسجيلات</option>
                                    <option value="paid" <?php echo $filter === 'paid' ? 'selected' : ''; ?>>مدفوعة</option>
                                    <option value="pending" <?php echo $filter === 'pending' ? 'selected' : ''; ?>>معلقة</option>
                                    <option value="accessible" <?php echo $filter === 'accessible' ? 'selected' : ''; ?>>متاحة الوصول</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       placeholder="البحث بالطالب أو الدورة..." 
                                       value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-1"></i>
                                    بحث
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Enrollments Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="fw-bold mb-0">
                            <i class="fas fa-clipboard-list me-2"></i>
                            قائمة التسجيلات (<?php echo count($enrollments); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($enrollments)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-clipboard-list fa-4x text-muted mb-3"></i>
                                <h4 class="text-muted">لا توجد تسجيلات</h4>
                                <p class="text-muted">لا توجد تسجيلات تطابق معايير البحث</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>معلومات الطالب</th>
                                            <th>الدورة</th>
                                            <th>تاريخ التسجيل</th>
                                            <th>حالة الدفع</th>
                                            <th>الوصول</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($enrollments as $enrollment): ?>
                                            <tr>
                                                <td>
                                                    <div>
                                                        <h6 class="fw-bold mb-1"><?php echo htmlspecialchars($enrollment['student_name']); ?></h6>
                                                        <small class="text-muted"><?php echo htmlspecialchars($enrollment['student_email']); ?></small><br>
                                                        <small class="text-primary">رقم: <?php echo htmlspecialchars($enrollment['student_id']); ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        <h6 class="fw-bold mb-1"><?php echo htmlspecialchars($enrollment['course_name']); ?></h6>
                                                        <small class="text-muted">المدرب: <?php echo htmlspecialchars($enrollment['instructor']); ?></small><br>
                                                        <small class="text-success fw-bold"><?php echo formatPrice($enrollment['price']); ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <small><?php echo formatDate($enrollment['enrollment_date'], 'Y-m-d H:i'); ?></small>
                                                </td>
                                                <td>
                                                    <span class="status-badge status-<?php echo $enrollment['payment_status'] === 'مدفوع' ? 'paid' : ($enrollment['payment_status'] === 'مرفوض' ? 'rejected' : 'pending'); ?>">
                                                        <?php echo $enrollment['payment_status']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="status-badge access-<?php echo $enrollment['course_access'] === 'متاح' ? 'available' : 'unavailable'; ?>">
                                                        <?php echo $enrollment['course_access']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <?php if ($enrollment['payment_status'] === 'معلق'): ?>
                                                            <form method="POST" class="d-inline">
                                                                <input type="hidden" name="enrollment_id" value="<?php echo $enrollment['id']; ?>">
                                                                <input type="hidden" name="action" value="approve_payment">
                                                                <button type="submit" class="btn btn-sm btn-success" 
                                                                        onclick="return confirm('هل أنت متأكد من قبول الدفع؟')" title="قبول الدفع">
                                                                    <i class="fas fa-check"></i>
                                                                </button>
                                                            </form>
                                                            <form method="POST" class="d-inline">
                                                                <input type="hidden" name="enrollment_id" value="<?php echo $enrollment['id']; ?>">
                                                                <input type="hidden" name="action" value="reject_payment">
                                                                <button type="submit" class="btn btn-sm btn-danger" 
                                                                        onclick="return confirm('هل أنت متأكد من رفض الدفع؟')" title="رفض الدفع">
                                                                    <i class="fas fa-times"></i>
                                                                </button>
                                                            </form>
                                                        <?php endif; ?>
                                                        
                                                        <?php if ($enrollment['payment_status'] === 'مدفوع'): ?>
                                                            <?php if ($enrollment['course_access'] === 'غير متاح'): ?>
                                                                <form method="POST" class="d-inline">
                                                                    <input type="hidden" name="enrollment_id" value="<?php echo $enrollment['id']; ?>">
                                                                    <input type="hidden" name="action" value="enable_access">
                                                                    <button type="submit" class="btn btn-sm btn-primary" 
                                                                            onclick="return confirm('هل أنت متأكد من تفعيل الوصول؟')" title="تفعيل الوصول">
                                                                        <i class="fas fa-unlock"></i>
                                                                    </button>
                                                                </form>
                                                            <?php else: ?>
                                                                <form method="POST" class="d-inline">
                                                                    <input type="hidden" name="enrollment_id" value="<?php echo $enrollment['id']; ?>">
                                                                    <input type="hidden" name="action" value="disable_access">
                                                                    <button type="submit" class="btn btn-sm btn-warning" 
                                                                            onclick="return confirm('هل أنت متأكد من إلغاء الوصول؟')" title="إلغاء الوصول">
                                                                        <i class="fas fa-lock"></i>
                                                                    </button>
                                                                </form>
                                                            <?php endif; ?>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
