<?php
/**
 * إعدادات قاعدة البيانات البسيطة
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'elzam_lms');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// متغير عام لقاعدة البيانات
$pdo = null;

try {
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ];

    $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);

} catch(PDOException $e) {
    // تسجيل الخطأ
    error_log("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());

    // عرض رسالة خطأ واضحة
    if (strpos($e->getMessage(), 'Unknown database') !== false) {
        die("
        <div style='font-family: Arial; text-align: center; padding: 50px; direction: rtl;'>
            <h2 style='color: red;'>خطأ في قاعدة البيانات</h2>
            <p>قاعدة البيانات 'elzam_lms' غير موجودة.</p>
            <p>يرجى إنشاؤها أولاً باستخدام الأمر:</p>
            <code style='background: #f0f0f0; padding: 10px; display: block; margin: 20px;'>
                CREATE DATABASE elzam_lms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            </code>
            <p><a href='test.php'>اختبار النظام</a></p>
        </div>
        ");
    } else {
        die("
        <div style='font-family: Arial; text-align: center; padding: 50px; direction: rtl;'>
            <h2 style='color: red;'>خطأ في الاتصال بقاعدة البيانات</h2>
            <p>" . htmlspecialchars($e->getMessage()) . "</p>
            <p><a href='test.php'>اختبار النظام</a></p>
        </div>
        ");
    }
}
?>
