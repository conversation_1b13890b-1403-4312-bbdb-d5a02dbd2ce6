<?php
// تحسين الأداء - تحميل الملفات فقط عند الحاجة
require_once 'config/config.php';

// فحص سريع للجلسة بدون تحميل ملفات إضافية
if (isset($_SESSION['user_type'])) {
    if ($_SESSION['user_type'] === 'student') {
        header('Location: student/dashboard.php');
        exit();
    } elseif ($_SESSION['user_type'] === 'admin') {
        header('Location: admin/index.php');
        exit();
    }
}

$message = '';
$message_type = '';
$login_type = $_GET['type'] ?? 'student'; // student أو admin

// معالجة تسجيل الدخول فقط عند الإرسال
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // تحميل ملفات المصادقة فقط عند الحاجة
    require_once 'includes/auth.php';

    $email_or_username = sanitizeInput($_POST['email_or_username']);
    $password = $_POST['password'];
    $type = $_POST['type'];

    if ($type === 'admin') {
        $result = loginAdmin($email_or_username, $password);
    } else {
        $result = loginStudent($email_or_username, $password);
    }

    if ($result['success']) {
        $message = getMessage('success', 'login');
        $message_type = 'success';

        // إعادة توجيه فوري بدلاً من الانتظار
        header("Location: " . ($type === 'admin' ? 'admin/index.php' : 'student/dashboard.php'));
        exit();
    } else {
        $message = $result['message'];
        $message_type = 'danger';
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            align-items: center;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        .btn-custom {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .form-label {
            font-weight: 600;
            color: #495057;
        }
        .login-tabs .nav-link {
            border-radius: 10px;
            margin: 0 5px;
            font-weight: 600;
        }
        .login-tabs .nav-link.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="card">
                    <div class="card-header bg-white text-center py-4">
                        <h3 class="fw-bold text-primary mb-0">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول
                        </h3>
                        <p class="text-muted mt-2">أدخل بياناتك للوصول إلى حسابك</p>
                    </div>
                    <div class="card-body p-5">
                        <!-- Login Type Tabs -->
                        <ul class="nav nav-pills login-tabs justify-content-center mb-4" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link <?php echo $login_type === 'student' ? 'active' : ''; ?>" 
                                        id="student-tab" data-bs-toggle="pill" data-bs-target="#student-login" 
                                        type="button" role="tab">
                                    <i class="fas fa-user-graduate me-1"></i>
                                    طالب
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link <?php echo $login_type === 'admin' ? 'active' : ''; ?>" 
                                        id="admin-tab" data-bs-toggle="pill" data-bs-target="#admin-login" 
                                        type="button" role="tab">
                                    <i class="fas fa-user-shield me-1"></i>
                                    مشرف
                                </button>
                            </li>
                        </ul>

                        <?php if ($message): ?>
                            <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                                <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                                <?php echo $message; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <div class="tab-content">
                            <!-- Student Login -->
                            <div class="tab-pane fade <?php echo $login_type === 'student' ? 'show active' : ''; ?>" 
                                 id="student-login" role="tabpanel">
                                <form method="POST" id="studentLoginForm">
                                    <input type="hidden" name="type" value="student">
                                    
                                    <div class="mb-3">
                                        <label for="student_email" class="form-label">
                                            <i class="fas fa-envelope me-1"></i>
                                            البريد الإلكتروني
                                        </label>
                                        <input type="email" class="form-control" id="student_email" 
                                               name="email_or_username" placeholder="أدخل البريد الإلكتروني" required>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <label for="student_password" class="form-label">
                                            <i class="fas fa-lock me-1"></i>
                                            كلمة المرور
                                        </label>
                                        <input type="password" class="form-control" id="student_password" 
                                               name="password" placeholder="أدخل كلمة المرور" required>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-custom btn-lg">
                                            <i class="fas fa-sign-in-alt me-2"></i>
                                            دخول الطالب
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <!-- Admin Login -->
                            <div class="tab-pane fade <?php echo $login_type === 'admin' ? 'show active' : ''; ?>" 
                                 id="admin-login" role="tabpanel">
                                <form method="POST" id="adminLoginForm">
                                    <input type="hidden" name="type" value="admin">
                                    
                                    <div class="mb-3">
                                        <label for="admin_username" class="form-label">
                                            <i class="fas fa-user me-1"></i>
                                            اسم المستخدم أو البريد الإلكتروني
                                        </label>
                                        <input type="text" class="form-control" id="admin_username" 
                                               name="email_or_username" placeholder="أدخل اسم المستخدم" required>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <label for="admin_password" class="form-label">
                                            <i class="fas fa-lock me-1"></i>
                                            كلمة المرور
                                        </label>
                                        <input type="password" class="form-control" id="admin_password" 
                                               name="password" placeholder="أدخل كلمة المرور" required>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-custom btn-lg">
                                            <i class="fas fa-shield-alt me-2"></i>
                                            دخول المشرف
                                        </button>
                                    </div>
                                </form>
                                
                                <div class="mt-3 p-3 bg-light rounded">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        <strong>بيانات تجريبية:</strong><br>
                                        اسم المستخدم: admin<br>
                                        كلمة المرور: admin123
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <div id="student-links" class="<?php echo $login_type === 'student' ? '' : 'd-none'; ?>">
                                <p class="text-muted">
                                    ليس لديك حساب؟ 
                                    <a href="register.php" class="text-primary text-decoration-none fw-bold">
                                        سجل الآن
                                    </a>
                                </p>
                            </div>
                            <a href="index.php" class="text-muted text-decoration-none">
                                <i class="fas fa-arrow-right me-1"></i>
                                العودة للصفحة الرئيسية
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // إظهار/إخفاء رابط التسجيل حسب نوع المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            const studentTab = document.getElementById('student-tab');
            const adminTab = document.getElementById('admin-tab');
            const studentLinks = document.getElementById('student-links');
            
            studentTab.addEventListener('click', function() {
                studentLinks.classList.remove('d-none');
            });
            
            adminTab.addEventListener('click', function() {
                studentLinks.classList.add('d-none');
            });
        });

        // تحديث URL عند تغيير التبويب
        document.getElementById('admin-tab').addEventListener('click', function() {
            history.pushState(null, null, 'login.php?type=admin');
        });
        
        document.getElementById('student-tab').addEventListener('click', function() {
            history.pushState(null, null, 'login.php');
        });
    </script>
</body>
</html>
