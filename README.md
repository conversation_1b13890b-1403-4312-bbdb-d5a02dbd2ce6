# نظام إدارة الدورات التدريبية - الزام

نظام إدارة دورات تدريبية (LMS) متكامل مطور بـ PHP و MySQL مع دعم كامل للغة العربية.

## المميزات الرئيسية

### 🎓 واجهة الطالب
- تسجيل طالب جديد مع رفع البطاقة الجامعية
- تسجيل دخول آمن
- عرض الدورات المتاحة
- التسجيل في الدورات
- نظام دفع إلكتروني (تجريبي)
- لوحة تحكم شخصية

### 👨‍💼 لوحة تحكم المشرف
- مراجعة طلبات الطلاب الجديدة (قبول/رفض/تعليق)
- إدارة الدورات التدريبية (CRUD)
- متابعة التسجيلات والمدفوعات
- سجل الأنشطة والأمان
- إرسال الإشعارات

### 🔒 نظام الأمان
- تشفير كلمات المرور (BCRYPT)
- حماية من SQL Injection (Prepared Statements)
- التحقق من ملفات الصور
- تسجيل جميع الأنشطة مع IP
- جلسات آمنة مع انتهاء صلاحية

## متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx
- مكتبات PHP: PDO, GD, mbstring

## التثبيت والإعداد

### 1. تحضير البيئة
```bash
# تأكد من تشغيل XAMPP أو WAMP
# أو أي خادم ويب محلي آخر
```

### 2. إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE elzam_lms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استيراد الهيكل
mysql -u root -p elzam_lms < sql/database.sql
```

### 3. إعداد الملفات
```bash
# نسخ المشروع إلى مجلد الخادم
cp -r elzam-web-site/ /path/to/htdocs/

# تعديل إعدادات قاعدة البيانات في config/database.php
```

### 4. إعداد الصلاحيات
```bash
# إعطاء صلاحيات الكتابة لمجلد الرفع
chmod 755 uploads/
chmod 755 uploads/university_cards/
chmod 755 uploads/course_images/
```

## الاستخدام

### الوصول للنظام
- الصفحة الرئيسية: `http://localhost/elzam-web-site/`
- تسجيل طالب جديد: `http://localhost/elzam-web-site/register.php`
- تسجيل الدخول: `http://localhost/elzam-web-site/login.php`
- لوحة الإدارة: `http://localhost/elzam-web-site/login.php?type=admin`

### بيانات المشرف الافتراضية
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`
- البريد الإلكتروني: `<EMAIL>`

### خطوات استخدام النظام

#### للطلاب:
1. **التسجيل**: املأ النموذج مع رفع البطاقة الجامعية
2. **انتظار الموافقة**: سيتم مراجعة طلبك من قبل المشرف
3. **تسجيل الدخول**: بعد الموافقة، سجل دخولك
4. **تصفح الدورات**: اختر الدورة المناسبة
5. **التسجيل والدفع**: أكمل عملية التسجيل والدفع
6. **بدء التعلم**: احصل على الوصول للدورة

#### للمشرفين:
1. **تسجيل الدخول**: استخدم بيانات المشرف
2. **مراجعة الطلاب**: قبول أو رفض طلبات التسجيل
3. **إدارة الدورات**: إضافة وتعديل الدورات
4. **متابعة النشاط**: مراقبة التسجيلات والمدفوعات

## هيكل المشروع

```
elzam-web-site/
├── config/                 # ملفات الإعدادات
│   ├── database.php        # إعدادات قاعدة البيانات
│   └── config.php          # الإعدادات العامة
├── includes/               # الملفات المساعدة
│   ├── functions.php       # الوظائف العامة
│   └── auth.php           # وظائف المصادقة
├── admin/                  # لوحة تحكم المشرف
│   ├── index.php          # الصفحة الرئيسية
│   ├── students.php       # إدارة الطلاب
│   └── courses.php        # إدارة الدورات
├── student/               # واجهة الطالب
│   ├── dashboard.php      # لوحة تحكم الطالب
│   ├── enroll.php         # التسجيل في الدورات
│   └── payment.php        # صفحة الدفع
├── uploads/               # ملفات الرفع
│   ├── university_cards/  # البطاقات الجامعية
│   └── course_images/     # صور الدورات
├── sql/                   # ملفات قاعدة البيانات
│   └── database.sql       # هيكل قاعدة البيانات
├── index.php              # الصفحة الرئيسية
├── register.php           # تسجيل طالب جديد
├── login.php              # تسجيل الدخول
└── logout.php             # تسجيل الخروج
```

## قاعدة البيانات

### الجداول الرئيسية:
- `students`: بيانات الطلاب
- `courses`: بيانات الدورات
- `enrollments`: تسجيلات الطلاب
- `payments`: سجلات المدفوعات
- `logs`: سجل الأنشطة
- `admins`: بيانات المشرفين

## الأمان

### التدابير المطبقة:
- تشفير كلمات المرور باستخدام `password_hash()`
- استخدام Prepared Statements لمنع SQL Injection
- التحقق من أنواع وأحجام الملفات المرفوعة
- تسجيل جميع الأنشطة مع عناوين IP
- جلسات آمنة مع انتهاء صلاحية
- تنظيف البيانات المدخلة

## التخصيص

### إضافة دورة جديدة:
1. سجل دخولك كمشرف
2. اذهب إلى "إدارة الدورات"
3. اضغط "إضافة دورة جديدة"
4. املأ البيانات المطلوبة

### تخصيص التصميم:
- عدل ملفات CSS في الصفحات
- استخدم Bootstrap 5 للتخصيص السريع
- غير الألوان في متغيرات CSS

## استكشاف الأخطاء

### مشاكل شائعة:

#### خطأ في الاتصال بقاعدة البيانات:
- تأكد من تشغيل MySQL
- راجع إعدادات قاعدة البيانات في `config/database.php`

#### مشكلة في رفع الملفات:
- تأكد من صلاحيات مجلد `uploads/`
- راجع إعدادات PHP لحجم الملفات

#### مشكلة في الترميز العربي:
- تأكد من إعداد UTF-8 في قاعدة البيانات
- راجع إعدادات الترميز في ملفات PHP

## الدعم والتطوير

### للحصول على المساعدة:
- راجع ملفات السجل في مجلد `logs/`
- تحقق من رسائل الخطأ في PHP
- راجع وثائق المشروع

### التطوير المستقبلي:
- إضافة نظام دفع حقيقي (PayPal/Stripe)
- تطوير تطبيق موبايل
- إضافة نظام تقييم الدورات
- تطوير نظام شهادات إلكترونية

## الترخيص

هذا المشروع مطور لأغراض تعليمية وتجريبية.

## لقطات الشاشة

### الصفحة الرئيسية
- تصميم جذاب مع Bootstrap 5
- واجهة متجاوبة لجميع الأجهزة
- دعم كامل للغة العربية

### لوحة تحكم الطالب
- عرض الإحصائيات الشخصية
- قائمة الدورات المسجل فيها
- تصفح الدورات المتاحة
- نظام دفع تجريبي

### لوحة تحكم المشرف
- إحصائيات شاملة للنظام
- إدارة الطلاب والدورات
- سجل الأنشطة المفصل
- نظام إشعارات

## الملفات الرئيسية

### ملفات الواجهة الأمامية
- `index.php` - الصفحة الرئيسية
- `register.php` - تسجيل طالب جديد
- `login.php` - تسجيل الدخول (طلاب ومشرفين)
- `assets/css/style.css` - ملف التصميم المخصص
- `assets/js/main.js` - ملف JavaScript التفاعلي

### ملفات الطلاب
- `student/dashboard.php` - لوحة تحكم الطالب
- `student/enroll.php` - التسجيل في الدورات
- `student/payment.php` - صفحة الدفع التجريبي

### ملفات المشرفين
- `admin/index.php` - لوحة تحكم المشرف
- `admin/students.php` - إدارة الطلاب
- `admin/courses.php` - إدارة الدورات
- `admin/logs.php` - سجل الأنشطة

### ملفات النظام
- `config/database.php` - إعدادات قاعدة البيانات
- `config/config.php` - الإعدادات العامة
- `includes/auth.php` - وظائف المصادقة
- `includes/functions.php` - الوظائف العامة

## المميزات التقنية

### الأمان
- تشفير كلمات المرور باستخدام `password_hash()`
- استخدام PDO مع Prepared Statements
- تنظيف البيانات المدخلة
- التحقق من صحة الملفات المرفوعة
- نظام جلسات آمن
- تسجيل شامل للأنشطة

### قاعدة البيانات
- تصميم محسن مع Foreign Keys
- دعم UTF-8 للغة العربية
- فهرسة مناسبة للاستعلامات
- علاقات مناسبة بين الجداول

### واجهة المستخدم
- تصميم متجاوب مع Bootstrap 5
- أيقونات Font Awesome
- تأثيرات CSS3 متقدمة
- JavaScript تفاعلي
- دعم كامل للغة العربية (RTL)

### الوظائف المتقدمة
- نظام إشعارات بالبريد الإلكتروني
- رفع وإدارة الملفات
- نظام فلترة وبحث متقدم
- إحصائيات ولوحات معلومات
- نظام سجل أنشطة شامل

## التطوير المستقبلي

### مميزات مقترحة
- [ ] تطبيق موبايل (React Native/Flutter)
- [ ] نظام دفع حقيقي (PayPal/Stripe)
- [ ] نظام شهادات إلكترونية
- [ ] منتدى للطلاب والمدربين
- [ ] نظام تقييم الدورات
- [ ] تقارير متقدمة وتحليلات
- [ ] نظام إشعارات فورية
- [ ] دعم الفيديو المباشر
- [ ] نظام امتحانات إلكترونية
- [ ] تطبيق PWA

### تحسينات تقنية
- [ ] تحسين الأداء والتخزين المؤقت
- [ ] إضافة API RESTful
- [ ] دعم Docker للنشر
- [ ] اختبارات تلقائية
- [ ] نظام CI/CD
- [ ] مراقبة الأداء
- [ ] نسخ احتياطية تلقائية

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. كتابة كود نظيف ومعلق
4. اختبار التغييرات
5. إرسال Pull Request

## الدعم

للحصول على الدعم:
- راجع ملف README
- تحقق من Issues في GitHub
- راسلنا عبر البريد الإلكتروني

---

**ملاحظة**: هذا نظام تجريبي لأغراض التعلم والتطوير. لا تستخدمه في بيئة إنتاج بدون مراجعة أمنية شاملة.
