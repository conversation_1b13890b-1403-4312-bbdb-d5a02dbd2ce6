<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول المشرف
requireAdminLogin();

$message = '';
$message_type = '';

// معالجة إضافة/تعديل الدورة
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add' || $action === 'edit') {
        $course_data = [
            'course_name' => sanitizeInput($_POST['course_name']),
            'instructor' => sanitizeInput($_POST['instructor']),
            'duration' => sanitizeInput($_POST['duration']),
            'expected_start_date' => $_POST['expected_start_date'],
            'price' => (float)$_POST['price'],
            'description' => sanitizeInput($_POST['description']),
            'course_link' => sanitizeInput($_POST['course_link']),
            'status' => $_POST['status']
        ];
        
        // رفع صورة الغلاف إذا تم اختيارها
        $cover_image = '';
        if (isset($_FILES['cover_image']) && $_FILES['cover_image']['error'] === UPLOAD_ERR_OK) {
            $upload_result = uploadImage($_FILES['cover_image'], COURSE_IMAGES_PATH);
            if ($upload_result['success']) {
                $cover_image = $upload_result['filename'];
            } else {
                $message = $upload_result['message'];
                $message_type = 'danger';
            }
        }
        
        if (!$message) {
            try {
                if ($action === 'add') {
                    $stmt = $pdo->prepare("
                        INSERT INTO courses (course_name, instructor, duration, expected_start_date, price, description, course_link, status, cover_image) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $course_data['course_name'],
                        $course_data['instructor'],
                        $course_data['duration'],
                        $course_data['expected_start_date'],
                        $course_data['price'],
                        $course_data['description'],
                        $course_data['course_link'],
                        $course_data['status'],
                        $cover_image
                    ]);
                    
                    logActivity(null, $_SESSION['admin_id'], 'إضافة دورة', "تم إضافة دورة: {$course_data['course_name']}");
                    $message = 'تم إضافة الدورة بنجاح';
                    $message_type = 'success';
                    
                } elseif ($action === 'edit') {
                    $course_id = (int)$_POST['course_id'];
                    
                    if ($cover_image) {
                        $stmt = $pdo->prepare("
                            UPDATE courses SET course_name=?, instructor=?, duration=?, expected_start_date=?, 
                                   price=?, description=?, course_link=?, status=?, cover_image=? 
                            WHERE id=?
                        ");
                        $stmt->execute([
                            $course_data['course_name'],
                            $course_data['instructor'],
                            $course_data['duration'],
                            $course_data['expected_start_date'],
                            $course_data['price'],
                            $course_data['description'],
                            $course_data['course_link'],
                            $course_data['status'],
                            $cover_image,
                            $course_id
                        ]);
                    } else {
                        $stmt = $pdo->prepare("
                            UPDATE courses SET course_name=?, instructor=?, duration=?, expected_start_date=?, 
                                   price=?, description=?, course_link=?, status=? 
                            WHERE id=?
                        ");
                        $stmt->execute([
                            $course_data['course_name'],
                            $course_data['instructor'],
                            $course_data['duration'],
                            $course_data['expected_start_date'],
                            $course_data['price'],
                            $course_data['description'],
                            $course_data['course_link'],
                            $course_data['status'],
                            $course_id
                        ]);
                    }
                    
                    logActivity(null, $_SESSION['admin_id'], 'تعديل دورة', "تم تعديل دورة: {$course_data['course_name']}");
                    $message = 'تم تحديث الدورة بنجاح';
                    $message_type = 'success';
                }
            } catch (PDOException $e) {
                error_log("خطأ في إدارة الدورة: " . $e->getMessage());
                $message = 'حدث خطأ في حفظ الدورة';
                $message_type = 'danger';
            }
        }
    }
    
    // حذف دورة
    elseif ($action === 'delete') {
        $course_id = (int)$_POST['course_id'];
        
        try {
            $stmt = $pdo->prepare("DELETE FROM courses WHERE id = ?");
            $stmt->execute([$course_id]);
            
            logActivity(null, $_SESSION['admin_id'], 'حذف دورة', "تم حذف دورة رقم: $course_id");
            $message = 'تم حذف الدورة بنجاح';
            $message_type = 'success';
        } catch (PDOException $e) {
            error_log("خطأ في حذف الدورة: " . $e->getMessage());
            $message = 'حدث خطأ في حذف الدورة';
            $message_type = 'danger';
        }
    }
}

// جلب جميع الدورات
try {
    $stmt = $pdo->query("SELECT * FROM courses ORDER BY created_at DESC");
    $courses = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("خطأ في جلب الدورات: " . $e->getMessage());
    $courses = [];
}

// جلب بيانات الدورة للتعديل
$edit_course = null;
if (isset($_GET['edit'])) {
    $edit_id = (int)$_GET['edit'];
    try {
        $stmt = $pdo->prepare("SELECT * FROM courses WHERE id = ?");
        $stmt->execute([$edit_id]);
        $edit_course = $stmt->fetch();
    } catch (PDOException $e) {
        error_log("خطأ في جلب بيانات الدورة: " . $e->getMessage());
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الدورات - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 10px;
            margin: 5px 0;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover {
            background-color: rgba(255,255,255,0.2);
            color: white;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .course-image {
            width: 80px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .status-available { background-color: #28a745; color: #fff; }
        .status-unavailable { background-color: #dc3545; color: #fff; }
        .status-completed { background-color: #6c757d; color: #fff; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <i class="fas fa-shield-alt fa-3x mb-2"></i>
                    <h5 class="fw-bold">لوحة الإدارة</h5>
                </div>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة التحكم
                    </a>
                    <a class="nav-link" href="students.php">
                        <i class="fas fa-users me-2"></i>
                        إدارة الطلاب
                    </a>
                    <a class="nav-link" href="courses.php" style="background-color: rgba(255,255,255,0.2); color: white;">
                        <i class="fas fa-book me-2"></i>
                        إدارة الدورات
                    </a>
                    <a class="nav-link" href="enrollments.php">
                        <i class="fas fa-clipboard-list me-2"></i>
                        التسجيلات
                    </a>
                    <a class="nav-link" href="payments.php">
                        <i class="fas fa-credit-card me-2"></i>
                        المدفوعات
                    </a>
                    <a class="nav-link" href="logs.php">
                        <i class="fas fa-history me-2"></i>
                        سجل الأنشطة
                    </a>
                    <hr class="my-3">
                    <a class="nav-link text-warning" href="../logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>
                        تسجيل الخروج
                    </a>
                </nav>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="fw-bold">إدارة الدورات</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#courseModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة دورة جديدة
                    </button>
                </div>
                
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Courses Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="fw-bold mb-0">
                            <i class="fas fa-book me-2"></i>
                            قائمة الدورات (<?php echo count($courses); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($courses)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-book fa-4x text-muted mb-3"></i>
                                <h4 class="text-muted">لا توجد دورات</h4>
                                <p class="text-muted">ابدأ بإضافة دورة جديدة</p>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#courseModal">
                                    <i class="fas fa-plus me-2"></i>
                                    إضافة دورة جديدة
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الصورة</th>
                                            <th>معلومات الدورة</th>
                                            <th>المدرب</th>
                                            <th>السعر</th>
                                            <th>تاريخ البدء</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($courses as $course): ?>
                                            <tr>
                                                <td>
                                                    <?php if ($course['cover_image']): ?>
                                                        <img src="../uploads/course_images/<?php echo $course['cover_image']; ?>" 
                                                             class="course-image" alt="صورة الدورة">
                                                    <?php else: ?>
                                                        <div class="course-image bg-light d-flex align-items-center justify-content-center">
                                                            <i class="fas fa-book text-muted"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div>
                                                        <h6 class="fw-bold mb-1"><?php echo htmlspecialchars($course['course_name']); ?></h6>
                                                        <small class="text-muted"><?php echo htmlspecialchars($course['duration']); ?></small>
                                                        <?php if ($course['description']): ?>
                                                            <br><small class="text-muted"><?php echo htmlspecialchars(substr($course['description'], 0, 50)) . '...'; ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                                <td><?php echo htmlspecialchars($course['instructor']); ?></td>
                                                <td class="fw-bold text-success"><?php echo formatPrice($course['price']); ?></td>
                                                <td><?php echo formatDate($course['expected_start_date'], 'Y-m-d'); ?></td>
                                                <td>
                                                    <span class="status-badge status-<?php echo $course['status'] === 'متاح' ? 'available' : ($course['status'] === 'غير متاح' ? 'unavailable' : 'completed'); ?>">
                                                        <?php echo $course['status']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button class="btn btn-sm btn-outline-primary" 
                                                                onclick="editCourse(<?php echo htmlspecialchars(json_encode($course)); ?>)">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <form method="POST" class="d-inline">
                                                            <input type="hidden" name="action" value="delete">
                                                            <input type="hidden" name="course_id" value="<?php echo $course['id']; ?>">
                                                            <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                                    onclick="return confirm('هل أنت متأكد من حذف هذه الدورة؟')">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Modal -->
    <div class="modal fade" id="courseModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="courseModalTitle">إضافة دورة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" enctype="multipart/form-data" id="courseForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" id="courseAction" value="add">
                        <input type="hidden" name="course_id" id="courseId">
                        
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="course_name" class="form-label">اسم الدورة *</label>
                                <input type="text" class="form-control" id="course_name" name="course_name" required>
                            </div>
                            <div class="col-md-6">
                                <label for="instructor" class="form-label">المدرب *</label>
                                <input type="text" class="form-control" id="instructor" name="instructor" required>
                            </div>
                            <div class="col-md-6">
                                <label for="duration" class="form-label">مدة الدورة *</label>
                                <input type="text" class="form-control" id="duration" name="duration" 
                                       placeholder="مثال: 4 أسابيع" required>
                            </div>
                            <div class="col-md-6">
                                <label for="price" class="form-label">السعر (ريال) *</label>
                                <input type="number" class="form-control" id="price" name="price" 
                                       step="0.01" min="0" required>
                            </div>
                            <div class="col-md-6">
                                <label for="expected_start_date" class="form-label">تاريخ البدء المتوقع *</label>
                                <input type="date" class="form-control" id="expected_start_date" 
                                       name="expected_start_date" required>
                            </div>
                            <div class="col-md-6">
                                <label for="status" class="form-label">حالة الدورة *</label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="متاح">متاح</option>
                                    <option value="غير متاح">غير متاح</option>
                                    <option value="مكتمل">مكتمل</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label for="description" class="form-label">وصف الدورة</label>
                                <textarea class="form-control" id="description" name="description" 
                                          rows="3" placeholder="وصف مختصر عن الدورة"></textarea>
                            </div>
                            <div class="col-12">
                                <label for="course_link" class="form-label">رابط الدورة</label>
                                <input type="url" class="form-control" id="course_link" name="course_link" 
                                       placeholder="https://example.com/course">
                            </div>
                            <div class="col-12">
                                <label for="cover_image" class="form-label">صورة الغلاف</label>
                                <input type="file" class="form-control" id="cover_image" name="cover_image" 
                                       accept="image/jpeg,image/png,image/jpg">
                                <div class="form-text">الحد الأقصى: 2MB. الأنواع المدعومة: JPG, PNG</div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary" id="courseSubmitBtn">
                            <i class="fas fa-save me-2"></i>
                            حفظ الدورة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // إعادة تعيين النموذج عند إغلاق النافذة المنبثقة
        document.getElementById('courseModal').addEventListener('hidden.bs.modal', function() {
            document.getElementById('courseForm').reset();
            document.getElementById('courseAction').value = 'add';
            document.getElementById('courseId').value = '';
            document.getElementById('courseModalTitle').textContent = 'إضافة دورة جديدة';
            document.getElementById('courseSubmitBtn').innerHTML = '<i class="fas fa-save me-2"></i>حفظ الدورة';
        });

        // دالة تعديل الدورة
        function editCourse(course) {
            document.getElementById('courseAction').value = 'edit';
            document.getElementById('courseId').value = course.id;
            document.getElementById('course_name').value = course.course_name;
            document.getElementById('instructor').value = course.instructor;
            document.getElementById('duration').value = course.duration;
            document.getElementById('price').value = course.price;
            document.getElementById('expected_start_date').value = course.expected_start_date;
            document.getElementById('status').value = course.status;
            document.getElementById('description').value = course.description || '';
            document.getElementById('course_link').value = course.course_link || '';
            
            document.getElementById('courseModalTitle').textContent = 'تعديل الدورة';
            document.getElementById('courseSubmitBtn').innerHTML = '<i class="fas fa-save me-2"></i>تحديث الدورة';
            
            new bootstrap.Modal(document.getElementById('courseModal')).show();
        }

        // التحقق من حجم الملف
        document.getElementById('cover_image').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file && file.size > <?php echo MAX_FILE_SIZE; ?>) {
                alert('حجم الملف كبير جداً! الحد الأقصى 2 ميجابايت.');
                e.target.value = '';
            }
        });
    </script>
</body>
</html>
