<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول الطالب
requireStudentLogin();

$enrollment_id = (int)($_GET['enrollment_id'] ?? 0);

if (!$enrollment_id) {
    header('Location: dashboard.php');
    exit();
}

// الحصول على معلومات التسجيل والدورة
try {
    $stmt = $pdo->prepare("
        SELECT e.*, c.course_name, c.instructor, c.price, c.description, s.full_name as student_name
        FROM enrollments e 
        JOIN courses c ON e.course_id = c.id 
        JOIN students s ON e.student_id = s.id 
        WHERE e.id = ? AND e.student_id = ?
    ");
    $stmt->execute([$enrollment_id, $_SESSION['student_id']]);
    $enrollment = $stmt->fetch();
    
    if (!$enrollment) {
        header('Location: dashboard.php');
        exit();
    }
    
    // إذا كان الدفع مكتملاً، إعادة توجيه
    if ($enrollment['payment_status'] === 'مدفوع') {
        header('Location: dashboard.php');
        exit();
    }
    
} catch (PDOException $e) {
    error_log("خطأ في جلب بيانات التسجيل: " . $e->getMessage());
    header('Location: dashboard.php');
    exit();
}

$message = '';
$message_type = '';

// معالجة الدفع التجريبي
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['simulate_payment'])) {
    // محاكاة دفع ناجح
    $transaction_id = 'TEST_' . uniqid();
    
    if (updatePaymentStatus($enrollment_id, 'مدفوع', $transaction_id)) {
        // تسجيل النشاط
        logActivity($_SESSION['student_id'], null, 'دفع', "تم دفع الدورة: {$enrollment['course_name']}");

        $message = getMessage('success', 'payment');
        $message_type = 'success';
        
        // إعادة توجيه بعد 3 ثوان
        header("refresh:3;url=dashboard.php");
    } else {
        $message = getMessage('error', 'payment_failed');
        $message_type = 'danger';
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إتمام الدفع - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        .btn-custom {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: bold;
        }
        .payment-method {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .payment-method:hover,
        .payment-method.selected {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        .price-display {
            font-size: 2rem;
            font-weight: bold;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-white text-center py-4">
                        <h3 class="fw-bold text-primary mb-0">
                            <i class="fas fa-credit-card me-2"></i>
                            إتمام الدفع
                        </h3>
                        <p class="text-muted mt-2">أكمل عملية الدفع للحصول على الوصول للدورة</p>
                    </div>
                    <div class="card-body p-5">
                        <?php if ($message): ?>
                            <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                                <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                                <?php echo $message; ?>
                                <?php if ($message_type === 'success'): ?>
                                    <br><small>سيتم إعادة توجيهك إلى لوحة التحكم...</small>
                                <?php endif; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <!-- Course Information -->
                        <div class="row mb-4">
                            <div class="col-md-8">
                                <h4 class="fw-bold text-primary"><?php echo htmlspecialchars($enrollment['course_name']); ?></h4>
                                <p class="text-muted mb-2">
                                    <i class="fas fa-user me-1"></i>
                                    المدرب: <?php echo htmlspecialchars($enrollment['instructor']); ?>
                                </p>
                                <p class="text-muted"><?php echo htmlspecialchars($enrollment['description']); ?></p>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="price-display"><?php echo formatPrice($enrollment['price']); ?></div>
                                <small class="text-muted">السعر الإجمالي</small>
                            </div>
                        </div>

                        <hr>

                        <!-- Payment Methods -->
                        <h5 class="fw-bold mb-3">اختر طريقة الدفع</h5>
                        
                        <form method="POST" id="paymentForm">
                            <!-- PayPal Simulation -->
                            <div class="payment-method" data-method="paypal">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="fab fa-paypal fa-2x text-primary"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="fw-bold mb-1">PayPal</h6>
                                        <small class="text-muted">ادفع بأمان باستخدام PayPal</small>
                                    </div>
                                    <div>
                                        <input type="radio" name="payment_method" value="paypal" class="form-check-input">
                                    </div>
                                </div>
                            </div>

                            <!-- Credit Card Simulation -->
                            <div class="payment-method" data-method="card">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="fas fa-credit-card fa-2x text-success"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="fw-bold mb-1">بطاقة ائتمانية</h6>
                                        <small class="text-muted">Visa, MasterCard, American Express</small>
                                    </div>
                                    <div>
                                        <input type="radio" name="payment_method" value="card" class="form-check-input">
                                    </div>
                                </div>
                            </div>

                            <!-- Bank Transfer Simulation -->
                            <div class="payment-method" data-method="bank">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="fas fa-university fa-2x text-info"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="fw-bold mb-1">تحويل بنكي</h6>
                                        <small class="text-muted">تحويل مباشر من البنك</small>
                                    </div>
                                    <div>
                                        <input type="radio" name="payment_method" value="bank" class="form-check-input">
                                    </div>
                                </div>
                            </div>

                            <!-- Test Payment Notice -->
                            <div class="alert alert-info mt-4" role="alert">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>ملاحظة:</strong> هذا نظام تجريبي. لن يتم خصم أي مبلغ فعلي. 
                                اختر أي طريقة دفع واضغط على "إتمام الدفع" لمحاكاة عملية دفع ناجحة.
                            </div>

                            <!-- Payment Summary -->
                            <div class="card bg-light mt-4">
                                <div class="card-body">
                                    <h6 class="fw-bold mb-3">ملخص الدفع</h6>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>سعر الدورة:</span>
                                        <span><?php echo formatPrice($enrollment['price']); ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>الضرائب:</span>
                                        <span>0.00 ريال</span>
                                    </div>
                                    <hr>
                                    <div class="d-flex justify-content-between fw-bold">
                                        <span>المجموع:</span>
                                        <span class="text-success"><?php echo formatPrice($enrollment['price']); ?></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Buttons -->
                            <div class="d-grid gap-2 mt-4">
                                <button type="submit" name="simulate_payment" class="btn btn-custom btn-lg" id="payButton" disabled>
                                    <i class="fas fa-lock me-2"></i>
                                    إتمام الدفع الآمن
                                </button>
                                <a href="dashboard.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    العودة إلى لوحة التحكم
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const paymentMethods = document.querySelectorAll('.payment-method');
            const payButton = document.getElementById('payButton');
            const radioButtons = document.querySelectorAll('input[name="payment_method"]');

            // Handle payment method selection
            paymentMethods.forEach(method => {
                method.addEventListener('click', function() {
                    // Remove selected class from all methods
                    paymentMethods.forEach(m => m.classList.remove('selected'));
                    
                    // Add selected class to clicked method
                    this.classList.add('selected');
                    
                    // Check the radio button
                    const radio = this.querySelector('input[type="radio"]');
                    radio.checked = true;
                    
                    // Enable pay button
                    payButton.disabled = false;
                    payButton.innerHTML = '<i class="fas fa-lock me-2"></i>إتمام الدفع الآمن';
                });
            });

            // Handle radio button changes
            radioButtons.forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.checked) {
                        payButton.disabled = false;
                    }
                });
            });

            // Handle form submission
            document.getElementById('paymentForm').addEventListener('submit', function(e) {
                const selectedMethod = document.querySelector('input[name="payment_method"]:checked');
                if (!selectedMethod) {
                    e.preventDefault();
                    alert('يرجى اختيار طريقة دفع');
                    return false;
                }

                // Show loading state
                payButton.disabled = true;
                payButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
            });
        });
    </script>
</body>
</html>
