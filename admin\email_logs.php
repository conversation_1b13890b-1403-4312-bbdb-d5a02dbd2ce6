<?php
require_once '../config/config.php';
require_once '../includes/auth.php';

// التحقق من تسجيل دخول المشرف
requireAdminLogin();

$log_file = __DIR__ . '/../logs/email_notifications.log';
$logs = [];

if (file_exists($log_file)) {
    $content = file_get_contents($log_file);
    $log_entries = explode('================================', $content);
    
    foreach ($log_entries as $entry) {
        $entry = trim($entry);
        if (!empty($entry)) {
            $logs[] = $entry;
        }
    }
    
    // عكس الترتيب لإظهار الأحدث أولاً
    $logs = array_reverse($logs);
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل الإشعارات - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 10px;
            margin: 5px 0;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover {
            background-color: rgba(255,255,255,0.2);
            color: white;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .log-entry {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <i class="fas fa-shield-alt fa-3x mb-2"></i>
                    <h5 class="fw-bold">لوحة الإدارة</h5>
                </div>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة التحكم
                    </a>
                    <a class="nav-link" href="students.php">
                        <i class="fas fa-users me-2"></i>
                        إدارة الطلاب
                    </a>
                    <a class="nav-link" href="courses.php">
                        <i class="fas fa-book me-2"></i>
                        إدارة الدورات
                    </a>
                    <a class="nav-link" href="enrollments.php">
                        <i class="fas fa-clipboard-list me-2"></i>
                        التسجيلات
                    </a>
                    <a class="nav-link" href="payments.php">
                        <i class="fas fa-credit-card me-2"></i>
                        المدفوعات
                    </a>
                    <a class="nav-link" href="logs.php">
                        <i class="fas fa-history me-2"></i>
                        سجل الأنشطة
                    </a>
                    <a class="nav-link" href="email_logs.php" style="background-color: rgba(255,255,255,0.2); color: white;">
                        <i class="fas fa-envelope me-2"></i>
                        سجل الإشعارات
                    </a>
                    <hr class="my-3">
                    <a class="nav-link text-warning" href="../logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>
                        تسجيل الخروج
                    </a>
                </nav>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="fw-bold">سجل الإشعارات البريدية</h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.php">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active">سجل الإشعارات</li>
                        </ol>
                    </nav>
                </div>
                
                <!-- Info Alert -->
                <div class="alert alert-info" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> في وضع التطوير، يتم حفظ الإشعارات في ملف السجل بدلاً من الإرسال الفعلي.
                    لتفعيل الإرسال الفعلي، راجع ملف <code>config/email.php</code>
                </div>
                
                <!-- Email Logs -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="fw-bold mb-0">
                            <i class="fas fa-envelope me-2"></i>
                            سجل الإشعارات (<?php echo count($logs); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($logs)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-envelope fa-4x text-muted mb-3"></i>
                                <h4 class="text-muted">لا توجد إشعارات</h4>
                                <p class="text-muted">لم يتم إرسال أي إشعارات بعد</p>
                            </div>
                        <?php else: ?>
                            <div style="max-height: 600px; overflow-y: auto;">
                                <?php foreach ($logs as $index => $log): ?>
                                    <div class="log-entry">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <strong>إشعار #<?php echo $index + 1; ?></strong>
                                            <small class="text-muted">
                                                <?php
                                                // استخراج التاريخ من السجل
                                                if (preg_match('/التاريخ: (.+)/', $log, $matches)) {
                                                    echo $matches[1];
                                                }
                                                ?>
                                            </small>
                                        </div>
                                        <pre style="white-space: pre-wrap; margin: 0;"><?php echo htmlspecialchars($log); ?></pre>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($logs)): ?>
                        <div class="card-footer">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    آخر تحديث: <?php echo file_exists($log_file) ? date('Y-m-d H:i:s', filemtime($log_file)) : 'غير محدد'; ?>
                                </small>
                                <a href="?clear_logs=1" class="btn btn-sm btn-outline-danger" 
                                   onclick="return confirm('هل أنت متأكد من حذف جميع السجلات؟')">
                                    <i class="fas fa-trash me-1"></i>
                                    مسح السجلات
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Email Setup Guide -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="fw-bold mb-0">
                            <i class="fas fa-cog me-2"></i>
                            إعداد البريد الإلكتروني
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="fw-bold">الحالة الحالية:</h6>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    وضع التطوير - الإشعارات تُحفظ في السجل
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="fw-bold">لتفعيل الإرسال الفعلي:</h6>
                                <ol class="small">
                                    <li>عدل ملف <code>config/email.php</code></li>
                                    <li>ضع بيانات SMTP الخاصة بك</li>
                                    <li>غير <code>SMTP_ENABLED</code> إلى <code>true</code></li>
                                    <li>ثبت PHPMailer: <code>composer require phpmailer/phpmailer</code></li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
// معالجة مسح السجلات
if (isset($_GET['clear_logs']) && $_GET['clear_logs'] == '1') {
    if (file_exists($log_file)) {
        unlink($log_file);
        header('Location: email_logs.php');
        exit();
    }
}
?>
