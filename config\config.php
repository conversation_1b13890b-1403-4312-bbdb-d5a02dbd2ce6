<?php
/**
 * إعدادات النظام العامة
 */

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// إعدادات النظام
define('SITE_NAME', 'نظام إدارة الدورات التدريبية - الزام');
define('SITE_URL', 'http://localhost/elzam-web-site');
define('ADMIN_EMAIL', '<EMAIL>');

// وضع التطوير (true = تطوير, false = إنتاج)
define('DEVELOPMENT_MODE', true);

// إعدادات البريد الإلكتروني
define('EMAIL_ENABLED', false); // تفعيل/إلغاء إرسال الإيميلات
define('EMAIL_LOG_ENABLED', true); // حفظ الإيميلات في السجل

// إعدادات الأداء
define('ENABLE_QUERY_CACHE', true);
define('SESSION_CACHE_EXPIRE', 180); // 3 ساعات

// إعدادات الملفات
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('UNIVERSITY_CARDS_PATH', UPLOAD_PATH . 'university_cards/');
define('COURSE_IMAGES_PATH', UPLOAD_PATH . 'course_images/');
define('MAX_FILE_SIZE', 2 * 1024 * 1024); // 2MB

// إعدادات الأمان
define('PASSWORD_MIN_LENGTH', 8);
define('SESSION_TIMEOUT', 3600); // ساعة واحدة

// أنواع الملفات المسموحة
define('ALLOWED_IMAGE_TYPES', ['image/jpeg', 'image/png', 'image/jpg']);
define('ALLOWED_IMAGE_EXTENSIONS', ['jpg', 'jpeg', 'png']);

// إعدادات البريد الإلكتروني (يمكن تخصيصها لاحقاً)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');

// إعدادات الدفع (وضع التجربة)
define('PAYPAL_MODE', 'sandbox'); // sandbox أو live
define('PAYPAL_CLIENT_ID', '');
define('PAYPAL_CLIENT_SECRET', '');

// رسائل النظام (كمتغير عام)
$GLOBALS['MESSAGES'] = [
    'success' => [
        'registration' => 'تم التسجيل بنجاح! سيتم مراجعة طلبك قريباً.',
        'login' => 'تم تسجيل الدخول بنجاح!',
        'logout' => 'تم تسجيل الخروج بنجاح!',
        'enrollment' => 'تم التسجيل في الدورة بنجاح!',
        'payment' => 'تم الدفع بنجاح!',
        'update' => 'تم التحديث بنجاح!',
        'delete' => 'تم الحذف بنجاح!'
    ],
    'error' => [
        'invalid_credentials' => 'بيانات الدخول غير صحيحة!',
        'access_denied' => 'ليس لديك صلاحية للوصول!',
        'file_upload' => 'خطأ في رفع الملف!',
        'file_size' => 'حجم الملف كبير جداً!',
        'file_type' => 'نوع الملف غير مدعوم!',
        'required_fields' => 'جميع الحقول مطلوبة!',
        'email_exists' => 'البريد الإلكتروني مستخدم مسبقاً!',
        'student_id_exists' => 'الرقم الجامعي مستخدم مسبقاً!',
        'payment_failed' => 'فشل في عملية الدفع!',
        'course_not_found' => 'الدورة غير موجودة!',
        'already_enrolled' => 'أنت مسجل في هذه الدورة مسبقاً!'
    ],
    'warning' => [
        'pending_approval' => 'حسابك معلق بانتظار موافقة المشرف.',
        'account_rejected' => 'تم رفض طلب التسجيل.',
        'payment_pending' => 'الدفع معلق بانتظار التأكيد.',
        'course_not_started' => 'الدورة لم تبدأ بعد.'
    ]
];

// دالة للحصول على الرسائل
function getMessage($type, $key) {
    return $GLOBALS['MESSAGES'][$type][$key] ?? 'رسالة غير محددة';
}

// دالة للحصول على عنوان IP الحقيقي
function getRealIpAddr() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    return $ip;
}

// دالة لتنظيف البيانات
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

// دالة لإنشاء مجلدات الرفع إذا لم تكن موجودة
function createUploadDirectories() {
    $directories = [
        UNIVERSITY_CARDS_PATH,
        COURSE_IMAGES_PATH
    ];
    
    foreach ($directories as $dir) {
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
        }
    }
}

// إنشاء مجلدات الرفع
createUploadDirectories();

// تضمين ملفات الاتصال بقاعدة البيانات
require_once __DIR__ . '/database.php';
?>
