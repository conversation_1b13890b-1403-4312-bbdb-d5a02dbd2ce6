<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول الطالب
requireStudentLogin();

$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $course_id = (int)$_POST['course_id'];
    $student_id = $_SESSION['student_id'];
    
    // التحقق من حالة الطالب
    $student = getStudentById($student_id);
    if ($student['status'] !== 'مقبول') {
        $message = 'يجب أن يكون حسابك مقبولاً للتسجيل في الدورات';
        $message_type = 'danger';
    } else {
        // تسجيل الطالب في الدورة
        $result = enrollStudent($student_id, $course_id);
        
        if ($result['success']) {
            $message = MESSAGES['success']['enrollment'];
            $message_type = 'success';
            
            // إعادة توجيه إلى صفحة الدفع
            header("Location: payment.php?enrollment_id=" . $result['enrollment_id']);
            exit();
        } else {
            $message = $result['message'];
            $message_type = 'danger';
        }
    }
}

// إعادة توجيه إلى لوحة التحكم مع الرسالة
$_SESSION['message'] = $message;
$_SESSION['message_type'] = $message_type;
header('Location: dashboard.php');
exit();
?>
