<?php
/**
 * ملف اختبار تسجيل الدخول
 */

require_once 'config/config.php';
require_once 'includes/auth.php';

echo "<h2>اختبار تسجيل دخول المشرف</h2>";

// بيانات الاختبار
$username = 'admin';
$password = 'admin123';

echo "<p><strong>اسم المستخدم:</strong> $username</p>";
echo "<p><strong>كلمة المرور:</strong> $password</p>";

// محاولة تسجيل الدخول
$result = loginAdmin($username, $password);

if ($result['success']) {
    echo "<div style='color: green; padding: 10px; border: 1px solid green; background: #f0fff0;'>";
    echo "✅ <strong>نجح تسجيل الدخول!</strong><br>";
    echo "اسم المشرف: " . htmlspecialchars($result['admin']['full_name']);
    echo "</div>";
} else {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
    echo "❌ <strong>فشل تسجيل الدخول!</strong><br>";
    echo "الخطأ: " . htmlspecialchars($result['message']);
    echo "</div>";
    
    // اختبار إضافي للتحقق من وجود المشرف
    echo "<h3>اختبار إضافي:</h3>";
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM admins WHERE username = ?");
        $stmt->execute([$username]);
        $admin = $stmt->fetch();
        
        if ($admin) {
            echo "<p>✅ المشرف موجود في قاعدة البيانات</p>";
            echo "<p><strong>البريد الإلكتروني:</strong> " . htmlspecialchars($admin['email']) . "</p>";
            echo "<p><strong>كلمة المرور المشفرة:</strong> " . htmlspecialchars($admin['password']) . "</p>";
            
            // اختبار التحقق من كلمة المرور
            if (password_verify($password, $admin['password'])) {
                echo "<p style='color: green;'>✅ كلمة المرور صحيحة</p>";
            } else {
                echo "<p style='color: red;'>❌ كلمة المرور غير صحيحة</p>";
                
                // إنشاء كلمة مرور جديدة
                $new_hash = password_hash($password, PASSWORD_DEFAULT);
                echo "<p><strong>كلمة المرور الجديدة المشفرة:</strong> $new_hash</p>";
                
                // تحديث كلمة المرور
                $update_stmt = $pdo->prepare("UPDATE admins SET password = ? WHERE username = ?");
                if ($update_stmt->execute([$new_hash, $username])) {
                    echo "<p style='color: green;'>✅ تم تحديث كلمة المرور في قاعدة البيانات</p>";
                    echo "<p><a href='test_login.php'>اختبر مرة أخرى</a></p>";
                } else {
                    echo "<p style='color: red;'>❌ فشل في تحديث كلمة المرور</p>";
                }
            }
        } else {
            echo "<p style='color: red;'>❌ المشرف غير موجود في قاعدة البيانات</p>";
            
            // إنشاء المشرف
            $hash = password_hash($password, PASSWORD_DEFAULT);
            $insert_stmt = $pdo->prepare("INSERT INTO admins (username, email, password, full_name) VALUES (?, ?, ?, ?)");
            if ($insert_stmt->execute([$username, '<EMAIL>', $hash, 'مشرف النظام'])) {
                echo "<p style='color: green;'>✅ تم إنشاء المشرف في قاعدة البيانات</p>";
                echo "<p><a href='test_login.php'>اختبر مرة أخرى</a></p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في إنشاء المشرف</p>";
            }
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

echo "<hr>";
echo "<p><a href='login.php?type=admin'>العودة لصفحة تسجيل الدخول</a></p>";
echo "<p><a href='index.php'>العودة للصفحة الرئيسية</a></p>";
?>
