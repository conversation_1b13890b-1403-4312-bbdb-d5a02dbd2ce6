<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول الطالب
requireStudentLogin();

$student = getStudentById($_SESSION['student_id']);
$student_courses = getStudentCourses($_SESSION['student_id']);
$available_courses = getAvailableCourses();

// إزالة الدورات المسجل فيها من القائمة المتاحة
$enrolled_course_ids = array_column($student_courses, 'id');
$available_courses = array_filter($available_courses, function($course) use ($enrolled_course_ids) {
    return !in_array($course['id'], $enrolled_course_ids);
});
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم الطالب - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 10px;
            margin: 5px 0;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.2);
            color: white;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .status-pending { background-color: #ffc107; color: #000; }
        .status-approved { background-color: #28a745; color: #fff; }
        .status-rejected { background-color: #dc3545; color: #fff; }
        .course-image {
            height: 200px;
            object-fit: cover;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <i class="fas fa-graduation-cap fa-3x mb-2"></i>
                    <h5 class="fw-bold">منصة الزام</h5>
                </div>
                
                <div class="mb-4">
                    <div class="text-center">
                        <i class="fas fa-user-circle fa-3x mb-2"></i>
                        <h6 class="fw-bold"><?php echo htmlspecialchars($student['full_name']); ?></h6>
                        <span class="status-badge status-<?php echo $student['status'] === 'مقبول' ? 'approved' : ($student['status'] === 'مرفوض' ? 'rejected' : 'pending'); ?>">
                            <?php echo $student['status']; ?>
                        </span>
                    </div>
                </div>
                
                <nav class="nav flex-column">
                    <a class="nav-link active" href="#dashboard" data-bs-toggle="pill">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة التحكم
                    </a>
                    <a class="nav-link" href="#my-courses" data-bs-toggle="pill">
                        <i class="fas fa-book me-2"></i>
                        دوراتي
                    </a>
                    <a class="nav-link" href="#available-courses" data-bs-toggle="pill">
                        <i class="fas fa-search me-2"></i>
                        الدورات المتاحة
                    </a>
                    <a class="nav-link" href="#profile" data-bs-toggle="pill">
                        <i class="fas fa-user me-2"></i>
                        الملف الشخصي
                    </a>
                    <hr class="my-3">
                    <a class="nav-link text-warning" href="../logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>
                        تسجيل الخروج
                    </a>
                </nav>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 p-4">
                <div class="tab-content">
                    <!-- Dashboard Tab -->
                    <div class="tab-pane fade show active" id="dashboard">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2 class="fw-bold">مرحباً، <?php echo htmlspecialchars($student['full_name']); ?></h2>
                            <div class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                <?php echo date('Y-m-d H:i'); ?>
                            </div>
                        </div>
                        
                        <?php if ($student['status'] === 'معلق'): ?>
                            <div class="alert alert-warning" role="alert">
                                <i class="fas fa-clock me-2"></i>
                                <strong>حسابك معلق!</strong> يرجى انتظار موافقة المشرف للوصول إلى جميع الميزات.
                            </div>
                        <?php elseif ($student['status'] === 'مرفوض'): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-times-circle me-2"></i>
                                <strong>تم رفض طلبك!</strong> يرجى التواصل مع الإدارة لمزيد من المعلومات.
                            </div>
                        <?php endif; ?>
                        
                        <!-- Statistics Cards -->
                        <div class="row g-4 mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-book fa-2x mb-2"></i>
                                        <h4 class="fw-bold"><?php echo count($student_courses); ?></h4>
                                        <p class="mb-0">دوراتي</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                                        <h4 class="fw-bold">
                                            <?php echo count(array_filter($student_courses, function($c) { return $c['payment_status'] === 'مدفوع'; })); ?>
                                        </h4>
                                        <p class="mb-0">مدفوعة</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-play-circle fa-2x mb-2"></i>
                                        <h4 class="fw-bold">
                                            <?php echo count(array_filter($student_courses, function($c) { return $c['course_access'] === 'متاح'; })); ?>
                                        </h4>
                                        <p class="mb-0">متاحة</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-clock fa-2x mb-2"></i>
                                        <h4 class="fw-bold">
                                            <?php echo count(array_filter($student_courses, function($c) { return $c['payment_status'] === 'معلق'; })); ?>
                                        </h4>
                                        <p class="mb-0">معلقة</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Recent Courses -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="fw-bold mb-0">
                                    <i class="fas fa-history me-2"></i>
                                    آخر الدورات
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($student_courses)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-book fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لم تسجل في أي دورة بعد</p>
                                        <a href="#available-courses" class="btn btn-primary" data-bs-toggle="pill">
                                            تصفح الدورات المتاحة
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>اسم الدورة</th>
                                                    <th>المدرب</th>
                                                    <th>تاريخ التسجيل</th>
                                                    <th>حالة الدفع</th>
                                                    <th>الوصول</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach (array_slice($student_courses, 0, 5) as $course): ?>
                                                    <tr>
                                                        <td class="fw-bold"><?php echo htmlspecialchars($course['course_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($course['instructor']); ?></td>
                                                        <td><?php echo formatDate($course['enrollment_date'], 'Y-m-d'); ?></td>
                                                        <td>
                                                            <span class="badge bg-<?php echo $course['payment_status'] === 'مدفوع' ? 'success' : 'warning'; ?>">
                                                                <?php echo $course['payment_status']; ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <?php if ($course['course_access'] === 'متاح'): ?>
                                                                <a href="#" class="btn btn-sm btn-success">
                                                                    <i class="fas fa-play me-1"></i>
                                                                    بدء الدورة
                                                                </a>
                                                            <?php else: ?>
                                                                <span class="text-muted">غير متاح</span>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- My Courses Tab -->
                    <div class="tab-pane fade" id="my-courses">
                        <h2 class="fw-bold mb-4">دوراتي</h2>
                        
                        <?php if (empty($student_courses)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-book fa-4x text-muted mb-3"></i>
                                <h4 class="text-muted">لا توجد دورات مسجلة</h4>
                                <p class="text-muted">لم تسجل في أي دورة بعد</p>
                                <a href="#available-courses" class="btn btn-primary" data-bs-toggle="pill">
                                    تصفح الدورات المتاحة
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="row g-4">
                                <?php foreach ($student_courses as $course): ?>
                                    <div class="col-md-6 col-lg-4">
                                        <div class="card h-100">
                                            <?php if ($course['cover_image']): ?>
                                                <img src="../uploads/course_images/<?php echo $course['cover_image']; ?>" 
                                                     class="card-img-top course-image" alt="صورة الدورة">
                                            <?php else: ?>
                                                <div class="card-img-top course-image bg-light d-flex align-items-center justify-content-center">
                                                    <i class="fas fa-book fa-3x text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div class="card-body">
                                                <h5 class="card-title fw-bold"><?php echo htmlspecialchars($course['course_name']); ?></h5>
                                                <p class="card-text">
                                                    <i class="fas fa-user me-1"></i>
                                                    <?php echo htmlspecialchars($course['instructor']); ?>
                                                </p>
                                                <p class="card-text">
                                                    <i class="fas fa-clock me-1"></i>
                                                    <?php echo htmlspecialchars($course['duration']); ?>
                                                </p>
                                                <div class="mb-2">
                                                    <span class="badge bg-<?php echo $course['payment_status'] === 'مدفوع' ? 'success' : 'warning'; ?>">
                                                        <?php echo $course['payment_status']; ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="card-footer bg-transparent">
                                                <?php if ($course['payment_status'] === 'معلق'): ?>
                                                    <a href="payment.php?enrollment_id=<?php echo $course['id']; ?>" 
                                                       class="btn btn-warning w-100">
                                                        <i class="fas fa-credit-card me-1"></i>
                                                        إتمام الدفع
                                                    </a>
                                                <?php elseif ($course['course_access'] === 'متاح'): ?>
                                                    <a href="<?php echo $course['course_link'] ?: '#'; ?>" 
                                                       class="btn btn-success w-100" target="_blank">
                                                        <i class="fas fa-play me-1"></i>
                                                        بدء الدورة
                                                    </a>
                                                <?php else: ?>
                                                    <button class="btn btn-secondary w-100" disabled>
                                                        <i class="fas fa-clock me-1"></i>
                                                        انتظر بدء الدورة
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Available Courses Tab -->
                    <div class="tab-pane fade" id="available-courses">
                        <h2 class="fw-bold mb-4">الدورات المتاحة</h2>
                        
                        <?php if ($student['status'] !== 'مقبول'): ?>
                            <div class="alert alert-warning" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                يجب أن يكون حسابك مقبولاً للتسجيل في الدورات.
                            </div>
                        <?php endif; ?>
                        
                        <?php if (empty($available_courses)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-search fa-4x text-muted mb-3"></i>
                                <h4 class="text-muted">لا توجد دورات متاحة</h4>
                                <p class="text-muted">جميع الدورات مسجل فيها أو غير متاحة حالياً</p>
                            </div>
                        <?php else: ?>
                            <div class="row g-4">
                                <?php foreach ($available_courses as $course): ?>
                                    <div class="col-md-6 col-lg-4">
                                        <div class="card h-100">
                                            <?php if ($course['cover_image']): ?>
                                                <img src="../uploads/course_images/<?php echo $course['cover_image']; ?>" 
                                                     class="card-img-top course-image" alt="صورة الدورة">
                                            <?php else: ?>
                                                <div class="card-img-top course-image bg-light d-flex align-items-center justify-content-center">
                                                    <i class="fas fa-book fa-3x text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div class="card-body">
                                                <h5 class="card-title fw-bold"><?php echo htmlspecialchars($course['course_name']); ?></h5>
                                                <p class="card-text text-muted"><?php echo htmlspecialchars($course['description']); ?></p>
                                                <div class="row text-center mb-3">
                                                    <div class="col-6">
                                                        <small class="text-muted">المدرب</small>
                                                        <div class="fw-bold"><?php echo htmlspecialchars($course['instructor']); ?></div>
                                                    </div>
                                                    <div class="col-6">
                                                        <small class="text-muted">المدة</small>
                                                        <div class="fw-bold"><?php echo htmlspecialchars($course['duration']); ?></div>
                                                    </div>
                                                </div>
                                                <div class="row text-center mb-3">
                                                    <div class="col-6">
                                                        <small class="text-muted">تاريخ البدء</small>
                                                        <div class="fw-bold"><?php echo formatDate($course['expected_start_date'], 'Y-m-d'); ?></div>
                                                    </div>
                                                    <div class="col-6">
                                                        <small class="text-muted">السعر</small>
                                                        <div class="fw-bold text-success"><?php echo formatPrice($course['price']); ?></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="card-footer bg-transparent">
                                                <?php if ($student['status'] === 'مقبول'): ?>
                                                    <form method="POST" action="enroll.php" class="d-inline w-100">
                                                        <input type="hidden" name="course_id" value="<?php echo $course['id']; ?>">
                                                        <button type="submit" class="btn btn-primary w-100">
                                                            <i class="fas fa-plus me-1"></i>
                                                            اشترك الآن
                                                        </button>
                                                    </form>
                                                <?php else: ?>
                                                    <button class="btn btn-secondary w-100" disabled>
                                                        <i class="fas fa-lock me-1"></i>
                                                        يتطلب موافقة الحساب
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Profile Tab -->
                    <div class="tab-pane fade" id="profile">
                        <h2 class="fw-bold mb-4">الملف الشخصي</h2>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="fw-bold mb-0">معلوماتي الشخصية</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <label class="form-label fw-bold">الرقم الجامعي</label>
                                                <p class="form-control-plaintext"><?php echo htmlspecialchars($student['student_id']); ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label fw-bold">الاسم الكامل</label>
                                                <p class="form-control-plaintext"><?php echo htmlspecialchars($student['full_name']); ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label fw-bold">المرحلة الجامعية</label>
                                                <p class="form-control-plaintext"><?php echo htmlspecialchars($student['university_stage']); ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label fw-bold">التخصص</label>
                                                <p class="form-control-plaintext"><?php echo htmlspecialchars($student['major']); ?></p>
                                            </div>
                                            <div class="col-12">
                                                <label class="form-label fw-bold">البريد الإلكتروني</label>
                                                <p class="form-control-plaintext"><?php echo htmlspecialchars($student['email']); ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label fw-bold">حالة الحساب</label>
                                                <p class="form-control-plaintext">
                                                    <span class="status-badge status-<?php echo $student['status'] === 'مقبول' ? 'approved' : ($student['status'] === 'مرفوض' ? 'rejected' : 'pending'); ?>">
                                                        <?php echo $student['status']; ?>
                                                    </span>
                                                </p>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label fw-bold">تاريخ التسجيل</label>
                                                <p class="form-control-plaintext"><?php echo formatDate($student['created_at'], 'Y-m-d'); ?></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="fw-bold mb-0">البطاقة الجامعية</h5>
                                    </div>
                                    <div class="card-body text-center">
                                        <?php if ($student['university_card']): ?>
                                            <img src="../uploads/university_cards/<?php echo $student['university_card']; ?>" 
                                                 class="img-fluid rounded" alt="البطاقة الجامعية" 
                                                 style="max-height: 300px;">
                                        <?php else: ?>
                                            <i class="fas fa-id-card fa-4x text-muted mb-3"></i>
                                            <p class="text-muted">لا توجد صورة</p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
