<?php
/**
 * وظائف المصادقة والتحقق
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/functions.php';

/**
 * تسجيل دخول الطالب
 */
function loginStudent($email, $password) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM students WHERE email = ?");
        $stmt->execute([$email]);
        $student = $stmt->fetch();
        
        if ($student && password_verify($password, $student['password'])) {
            // التحقق من حالة الحساب
            if ($student['status'] === 'مرفوض') {
                return ['success' => false, 'message' => MESSAGES['warning']['account_rejected']];
            }
            
            // إنشاء جلسة
            $_SESSION['student_id'] = $student['id'];
            $_SESSION['student_name'] = $student['full_name'];
            $_SESSION['student_status'] = $student['status'];
            $_SESSION['user_type'] = 'student';
            $_SESSION['last_activity'] = time();
            
            // تسجيل النشاط
            logActivity($student['id'], null, 'تسجيل دخول', 'تم تسجيل الدخول بنجاح');
            
            return ['success' => true, 'student' => $student];
        } else {
            // تسجيل محاولة دخول فاشلة
            logActivity(null, null, 'محاولة دخول فاشلة', "البريد الإلكتروني: $email");
            return ['success' => false, 'message' => MESSAGES['error']['invalid_credentials']];
        }
    } catch (PDOException $e) {
        error_log("خطأ في تسجيل دخول الطالب: " . $e->getMessage());
        return ['success' => false, 'message' => 'خطأ في النظام'];
    }
}

/**
 * تسجيل دخول المشرف
 */
function loginAdmin($username, $password) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM admins WHERE username = ? OR email = ?");
        $stmt->execute([$username, $username]);
        $admin = $stmt->fetch();
        
        if ($admin && password_verify($password, $admin['password'])) {
            // إنشاء جلسة
            $_SESSION['admin_id'] = $admin['id'];
            $_SESSION['admin_name'] = $admin['full_name'];
            $_SESSION['user_type'] = 'admin';
            $_SESSION['last_activity'] = time();
            
            // تسجيل النشاط
            logActivity(null, $admin['id'], 'تسجيل دخول مشرف', 'تم تسجيل الدخول بنجاح');
            
            return ['success' => true, 'admin' => $admin];
        } else {
            // تسجيل محاولة دخول فاشلة
            logActivity(null, null, 'محاولة دخول مشرف فاشلة', "اسم المستخدم: $username");
            return ['success' => false, 'message' => MESSAGES['error']['invalid_credentials']];
        }
    } catch (PDOException $e) {
        error_log("خطأ في تسجيل دخول المشرف: " . $e->getMessage());
        return ['success' => false, 'message' => 'خطأ في النظام'];
    }
}

/**
 * تسجيل طالب جديد
 */
function registerStudent($data) {
    global $pdo;
    
    try {
        // التحقق من البيانات
        $required_fields = ['student_id', 'full_name', 'university_stage', 'major', 'email', 'password'];
        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                return ['success' => false, 'message' => MESSAGES['error']['required_fields']];
            }
        }
        
        // التحقق من صحة البريد الإلكتروني
        if (!validateEmail($data['email'])) {
            return ['success' => false, 'message' => 'البريد الإلكتروني غير صحيح'];
        }
        
        // التحقق من قوة كلمة المرور
        if (!validatePassword($data['password'])) {
            return ['success' => false, 'message' => 'كلمة المرور يجب أن تكون 8 أحرف على الأقل'];
        }
        
        // التحقق من عدم وجود البريد الإلكتروني مسبقاً
        $stmt = $pdo->prepare("SELECT id FROM students WHERE email = ?");
        $stmt->execute([$data['email']]);
        if ($stmt->fetch()) {
            return ['success' => false, 'message' => MESSAGES['error']['email_exists']];
        }
        
        // التحقق من عدم وجود الرقم الجامعي مسبقاً
        $stmt = $pdo->prepare("SELECT id FROM students WHERE student_id = ?");
        $stmt->execute([$data['student_id']]);
        if ($stmt->fetch()) {
            return ['success' => false, 'message' => MESSAGES['error']['student_id_exists']];
        }
        
        // تشفير كلمة المرور
        $hashed_password = password_hash($data['password'], PASSWORD_DEFAULT);
        
        // إدراج الطالب الجديد
        $stmt = $pdo->prepare("
            INSERT INTO students (student_id, full_name, university_stage, major, university_card, email, password) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $data['student_id'],
            $data['full_name'],
            $data['university_stage'],
            $data['major'],
            $data['university_card'],
            $data['email'],
            $hashed_password
        ]);
        
        $student_id = $pdo->lastInsertId();
        
        // تسجيل النشاط
        logActivity($student_id, null, 'تسجيل جديد', 'تم إنشاء حساب جديد');
        
        return ['success' => true, 'student_id' => $student_id];
        
    } catch (PDOException $e) {
        error_log("خطأ في تسجيل الطالب: " . $e->getMessage());
        return ['success' => false, 'message' => 'خطأ في النظام'];
    }
}

/**
 * التحقق من تسجيل دخول الطالب
 */
function isStudentLoggedIn() {
    return isset($_SESSION['student_id']) && isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'student';
}

/**
 * التحقق من تسجيل دخول المشرف
 */
function isAdminLoggedIn() {
    return isset($_SESSION['admin_id']) && isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'admin';
}

/**
 * التحقق من انتهاء صلاحية الجلسة
 */
function checkSessionTimeout() {
    if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT)) {
        logout();
        return false;
    }
    $_SESSION['last_activity'] = time();
    return true;
}

/**
 * تسجيل الخروج
 */
function logout() {
    $user_type = $_SESSION['user_type'] ?? '';
    $user_id = '';
    
    if ($user_type === 'student') {
        $user_id = $_SESSION['student_id'] ?? '';
        logActivity($user_id, null, 'تسجيل خروج', 'تم تسجيل الخروج');
    } elseif ($user_type === 'admin') {
        $user_id = $_SESSION['admin_id'] ?? '';
        logActivity(null, $user_id, 'تسجيل خروج مشرف', 'تم تسجيل الخروج');
    }
    
    // إزالة جميع متغيرات الجلسة
    $_SESSION = array();
    
    // حذف ملف تعريف الارتباط للجلسة
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }
    
    // تدمير الجلسة
    session_destroy();
}

/**
 * إعادة توجيه المستخدم حسب نوعه
 */
function redirectUser() {
    if (isStudentLoggedIn()) {
        header('Location: student/dashboard.php');
        exit();
    } elseif (isAdminLoggedIn()) {
        header('Location: admin/index.php');
        exit();
    }
}

/**
 * حماية صفحات الطلاب
 */
function requireStudentLogin() {
    if (!isStudentLoggedIn() || !checkSessionTimeout()) {
        header('Location: ../login.php');
        exit();
    }
}

/**
 * حماية صفحات المشرفين
 */
function requireAdminLogin() {
    if (!isAdminLoggedIn() || !checkSessionTimeout()) {
        header('Location: ../login.php?type=admin');
        exit();
    }
}
?>
